/*
  Warnings:

  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `User` table. All the data in the column will be lost.
  - Added the required column `roleId` to the `User` table without a default value. This is not possible if the table is not empty.
  - The required column `userId` was added to the `User` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('employee', 'manager', 'auditor', 'projectManager', 'ceo', 'accountant', 'accountantManager', 'admin');

-- CreateEnum
CREATE TYPE "ThreadMessageType" AS ENUM ('message', 'log');

-- CreateEnum
CREATE TYPE "PurchaseOrderType" AS ENUM ('cash', 'installment');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('toReview', 'toApprove', 'toAudit', 'toPay', 'transferred', 'rejected', 'paid');

-- CreateEnum
CREATE TYPE "Currency" AS ENUM ('JOD', 'USD', 'EUR', 'GBP', 'AED');

-- CreateEnum
CREATE TYPE "VendorType" AS ENUM ('Company', 'Individual');

-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('bankTransfer', 'cheque', 'cash', 'CLIQ', 'eFAWATEERCOM');

-- AlterTable
ALTER TABLE "User" DROP CONSTRAINT "User_pkey",
DROP COLUMN "id",
ADD COLUMN     "roleId" TEXT NOT NULL,
ADD COLUMN     "userId" TEXT NOT NULL,
ALTER COLUMN "password" DROP NOT NULL,
ADD CONSTRAINT "User_pkey" PRIMARY KEY ("userId");

-- CreateTable
CREATE TABLE "Role" (
    "roleId" TEXT NOT NULL,
    "role" "UserRole" NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("roleId")
);

-- CreateTable
CREATE TABLE "PurchaseOrderThread" (
    "purchaseOrderThreadId" TEXT NOT NULL,
    "purchaseOrderId" TEXT NOT NULL,
    "message" TEXT NOT NULL DEFAULT '',
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "type" "ThreadMessageType" NOT NULL DEFAULT 'message',

    CONSTRAINT "PurchaseOrderThread_pkey" PRIMARY KEY ("purchaseOrderThreadId")
);

-- CreateTable
CREATE TABLE "PurchaseOrder" (
    "purchaseOrderId" TEXT NOT NULL,
    "userPrepareId" TEXT NOT NULL,
    "userReviewId" TEXT NOT NULL,
    "userApproveId" TEXT NOT NULL,
    "userAuditId" TEXT NOT NULL,
    "paymentType" "PurchaseOrderType" NOT NULL DEFAULT 'cash',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrder_pkey" PRIMARY KEY ("purchaseOrderId")
);

-- CreateTable
CREATE TABLE "PurchaseOrderDetails" (
    "purchaseOrderDetailId" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "installment" BOOLEAN NOT NULL DEFAULT false,
    "projectId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "paymentMethod" "PaymentMethod" NOT NULL,
    "nameOnCheque" TEXT,
    "identifier" TEXT,
    "iban" TEXT,
    "accountName" TEXT,
    "swiftCode" TEXT,
    "cliq" TEXT,
    "date" TIMESTAMP(3),
    "contactName" TEXT,
    "contactNumber" TEXT,
    "currency" "Currency" NOT NULL DEFAULT 'JOD',
    "totalAmount" DOUBLE PRECISION NOT NULL,
    "purchaseOrderId" TEXT NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrderDetails_pkey" PRIMARY KEY ("purchaseOrderDetailId")
);

-- CreateTable
CREATE TABLE "PurchaseOrderPayment" (
    "PurchaseOrderPaymentId" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "percentage" DOUBLE PRECISION NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "purchaseOrderDetailId" TEXT NOT NULL,
    "status" "PaymentStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrderPayment_pkey" PRIMARY KEY ("PurchaseOrderPaymentId")
);

-- CreateTable
CREATE TABLE "Project" (
    "projectId" TEXT NOT NULL,
    "projectName" TEXT NOT NULL,
    "closed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Project_pkey" PRIMARY KEY ("projectId")
);

-- CreateTable
CREATE TABLE "Company" (
    "companyId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "legalName" TEXT NOT NULL DEFAULT '',
    "companyPhone" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "salesTaxNumber" TEXT DEFAULT '',
    "nationalId" TEXT NOT NULL DEFAULT '',
    "address" TEXT NOT NULL DEFAULT '',
    "contactPerson" TEXT NOT NULL DEFAULT '',
    "contactPhone" TEXT NOT NULL DEFAULT '',
    "type" "VendorType" NOT NULL DEFAULT 'Company',
    "description" TEXT DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("companyId")
);

-- CreateTable
CREATE TABLE "PurchaseOrderItem" (
    "purchaseOrderItemId" TEXT NOT NULL,
    "description" TEXT,
    "priceNoTax" DOUBLE PRECISION NOT NULL,
    "priceTax" DOUBLE PRECISION NOT NULL,
    "taxAmount" DOUBLE PRECISION NOT NULL,
    "purchaseOrderDetailId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PurchaseOrderItem_pkey" PRIMARY KEY ("purchaseOrderItemId")
);

-- CreateTable
CREATE TABLE "Notification" (
    "notificationId" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "seen" BOOLEAN NOT NULL DEFAULT false,
    "opened" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "purchaseOrderId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("notificationId")
);

-- CreateTable
CREATE TABLE "Attachment" (
    "attachmentId" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "key" TEXT,
    "name" TEXT DEFAULT '',
    "type" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Attachment_pkey" PRIMARY KEY ("attachmentId")
);

-- CreateTable
CREATE TABLE "CompanyAttachment" (
    "companyAttachmentId" TEXT NOT NULL,
    "attachmentId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,

    CONSTRAINT "CompanyAttachment_pkey" PRIMARY KEY ("companyAttachmentId")
);

-- CreateTable
CREATE TABLE "PurchaseOrderAttachment" (
    "purchaseOrderAttachmentId" TEXT NOT NULL,
    "attachmentId" TEXT NOT NULL,
    "purchaseOrderId" TEXT NOT NULL,

    CONSTRAINT "PurchaseOrderAttachment_pkey" PRIMARY KEY ("purchaseOrderAttachmentId")
);

-- CreateIndex
CREATE UNIQUE INDEX "PurchaseOrderDetails_purchaseOrderId_key" ON "PurchaseOrderDetails"("purchaseOrderId");

-- CreateIndex
CREATE UNIQUE INDEX "PurchaseOrderPayment_purchaseOrderDetailId_key" ON "PurchaseOrderPayment"("purchaseOrderDetailId");

-- CreateIndex
CREATE UNIQUE INDEX "Project_projectName_key" ON "Project"("projectName");

-- CreateIndex
CREATE UNIQUE INDEX "Company_companyName_key" ON "Company"("companyName");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderThread" ADD CONSTRAINT "PurchaseOrderThread_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("purchaseOrderId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderThread" ADD CONSTRAINT "PurchaseOrderThread_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_userPrepareId_fkey" FOREIGN KEY ("userPrepareId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_userReviewId_fkey" FOREIGN KEY ("userReviewId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_userApproveId_fkey" FOREIGN KEY ("userApproveId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrder" ADD CONSTRAINT "PurchaseOrder_userAuditId_fkey" FOREIGN KEY ("userAuditId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderDetails" ADD CONSTRAINT "PurchaseOrderDetails_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("projectId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderDetails" ADD CONSTRAINT "PurchaseOrderDetails_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("companyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderDetails" ADD CONSTRAINT "PurchaseOrderDetails_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("purchaseOrderId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderPayment" ADD CONSTRAINT "PurchaseOrderPayment_purchaseOrderDetailId_fkey" FOREIGN KEY ("purchaseOrderDetailId") REFERENCES "PurchaseOrderDetails"("purchaseOrderDetailId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderItem" ADD CONSTRAINT "PurchaseOrderItem_purchaseOrderDetailId_fkey" FOREIGN KEY ("purchaseOrderDetailId") REFERENCES "PurchaseOrderDetails"("purchaseOrderDetailId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("purchaseOrderId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyAttachment" ADD CONSTRAINT "CompanyAttachment_attachmentId_fkey" FOREIGN KEY ("attachmentId") REFERENCES "Attachment"("attachmentId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyAttachment" ADD CONSTRAINT "CompanyAttachment_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("companyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderAttachment" ADD CONSTRAINT "PurchaseOrderAttachment_attachmentId_fkey" FOREIGN KEY ("attachmentId") REFERENCES "Attachment"("attachmentId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderAttachment" ADD CONSTRAINT "PurchaseOrderAttachment_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("purchaseOrderId") ON DELETE RESTRICT ON UPDATE CASCADE;
