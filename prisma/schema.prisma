// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Role {
    roleId String   @id @default(cuid())
    role   UserRole
    Users  User[]
}

enum UserRole {
    employee
    manager
    auditor
    projectManager
    ceo
    accountant
    accountantManager
    admin
}

model User {
    id        String   @id @default(cuid())
    username  String?  @unique
    email     String?  @unique
    password  String?
    roleId    String
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    role                 Role                   @relation(fields: [roleId], references: [roleId])
    PurchaseOrderThreads PurchaseOrderThread[]
    userPrepare          PurchaseOrder[]        @relation(name: "userPrepare")
    userReview           PurchaseOrder[]        @relation(name: "userReview")
    userApprove          PurchaseOrder[]        @relation(name: "userApprove")
    userAudit            PurchaseOrder[]        @relation(name: "userAudit")
    Notifications        Notification[]
    PurchaseOrderPayment PurchaseOrderPayment[]
}

model PurchaseOrderThread {
    purchaseOrderThreadId String   @id @default(cuid())
    purchaseOrderId       String
    message               String   @default("")
    id                    String
    createdAt             DateTime @default(now())

    purchaseOrder PurchaseOrder     @relation(fields: [purchaseOrderId], references: [purchaseOrderId])
    type          ThreadMessageType @default(message)
    user          User              @relation(fields: [id], references: [id])
}

enum ThreadMessageType {
    message
    log
}

enum PurchaseOrderType {
    cash
    installment
}

model PurchaseOrder {
    purchaseOrderId String @id @default(cuid())
    // status            PurchaseOrderStatus              @default(draft)
    referenceNumber Int    @default(autoincrement())

    isDraft       Boolean
    isPaid        Boolean
    userPrepareId String
    userPrepare   User    @relation(fields: [userPrepareId], references: [id], name: "userPrepare")
    userReviewId  String
    userReview    User    @relation(fields: [userReviewId], references: [id], name: "userReview")
    userApproveId String
    userApprove   User    @relation(fields: [userApproveId], references: [id], name: "userApprove")
    userAuditId   String
    userAudit     User    @relation(fields: [userAuditId], references: [id], name: "userAudit")

    paymentType PurchaseOrderType @default(cash)

    PurchaseOrderThreads PurchaseOrderThread[]

    PurchaseOrderDetails PurchaseOrderDetails?

    createdAt                DateTime                  @default(now())
    updatedAt                DateTime                  @updatedAt
    deletedAt                DateTime?
    Notifications            Notification[]
    purchaseOrderAttachments PurchaseOrderAttachment[]
}

model PurchaseOrderDetails {
    purchaseOrderDetailId String                 @id @default(cuid())
    description           String?
    createdAt             DateTime               @default(now())
    // installment           Boolean                @default(false)
    projectId             String
    project               Project                @relation(fields: [projectId], references: [projectId])
    companyId             String
    company               Company                @relation(fields: [companyId], references: [companyId])
    paymentMethod         PaymentMethod
    nameOnCheque          String?
    identifier            String?
    iban                  String?
    accountName           String?
    swiftCode             String?
    cliq                  String?
    // date                  DateTime?
    contactName           String?
    contactNumber         String?
    currency              Currency               @default(JOD)
    totalAmount           Float
    PurchaseOrderItems    PurchaseOrderItem[]
    PurchaseOrderPayments PurchaseOrderPayment[]

    purchaseOrderId String        @unique
    PurchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [purchaseOrderId])
    updatedAt       DateTime      @updatedAt
}

enum PaymentStatus {
    idle
    toReview
    toApprove
    toAudit
    toPay
    transferred
    rejected
    paid
}

model PurchaseOrderPayment {
    PurchaseOrderPaymentId String               @id @default(cuid())
    description            String
    amount                 Float
    percentage             Float
    date                   DateTime
    reviewedAt             DateTime?
    approvedAt             DateTime?
    auditedAt              DateTime?
    paidAt                 DateTime?
    paidUserId             String?
    paidBy                 User?                @relation(fields: [paidUserId], references: [id])
    purchaseOrderDetailId  String
    PurchaseOrderDetail    PurchaseOrderDetails @relation(fields: [purchaseOrderDetailId], references: [purchaseOrderDetailId])
    status                 PaymentStatus        @default(idle)
    createdAt              DateTime             @default(now())
    updatedAt              DateTime             @updatedAt
    Notifications          Notification[]
}

enum Currency {
    JOD
    USD
    EUR
    GBP
    AED
}

enum VendorType {
    Company
    Individual
}

enum PaymentMethod {
    bankTransfer
    cheque
    cash
    CLIQ
    eFAWATEERCOM
}

model Project {
    projectId            String                 @id @default(cuid())
    projectName          String                 @unique
    closed               Boolean                @default(false)
    PurchaseOrderDetails PurchaseOrderDetails[]
    createdAt            DateTime               @default(now())
    updatedAt            DateTime               @updatedAt
}

model Company {
    companyId      String     @id @default(cuid())
    companyName    String     @unique
    legalName      String     @default("")
    companyPhone   String     @default("")
    email          String     @default("")
    salesTaxNumber String?    @default("")
    nationalId     String     @default("")
    address        String     @default("")
    contactPerson  String     @default("")
    contactPhone   String     @default("")
    type           VendorType @default(Company)
    description    String?    @default("")

    paymentMethod PaymentMethod?
    nameOnCheque  String?
    identifier    String?
    iban          String?
    accountName   String?
    swiftCode     String?
    cliq          String?

    PurchaseOrderDetails PurchaseOrderDetails[]
    createdAt            DateTime               @default(now())
    updatedAt            DateTime               @updatedAt
    CompanyAttachments   CompanyAttachment[]
}

model PurchaseOrderItem {
    purchaseOrderItemId   String               @id @default(cuid())
    description           String?
    priceNoTax            Float
    priceTax              Float
    taxAmount             Float
    // date                  DateTime
    purchaseOrderDetailId String
    purchaseOrderDetail   PurchaseOrderDetails @relation(fields: [purchaseOrderDetailId], references: [purchaseOrderDetailId])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Notification {
    notificationId String  @id @default(cuid())
    text           String
    seen           Boolean @default(false)
    opened         Boolean @default(false)

    id   String
    user User   @relation(fields: [id], references: [id])

    purchaseOrderId        String
    purchaseOrder          PurchaseOrder        @relation(fields: [purchaseOrderId], references: [purchaseOrderId])
    PurchaseOrderPaymentId String
    purchaseOrderPayment   PurchaseOrderPayment @relation(fields: [PurchaseOrderPaymentId], references: [PurchaseOrderPaymentId])
    createdAt              DateTime             @default(now())
    updatedAt              DateTime             @updatedAt
}

model Attachment {
    attachmentId             String                    @id @default(cuid())
    url                      String
    key                      String?
    name                     String?                   @default("")
    type                     String
    description              String?
    createdAt                DateTime                  @default(now())
    updatedAt                DateTime                  @updatedAt
    CompanyAttachments       CompanyAttachment[]
    PurchaseOrderAttachments PurchaseOrderAttachment[]
}

model CompanyAttachment {
    companyAttachmentId String     @id @default(cuid())
    attachmentId        String
    Attachment          Attachment @relation(fields: [attachmentId], references: [attachmentId])
    companyId           String
    company             Company    @relation(fields: [companyId], references: [companyId])
}

model PurchaseOrderAttachment {
    purchaseOrderAttachmentId String        @id @default(cuid())
    attachmentId              String
    Attachment                Attachment    @relation(fields: [attachmentId], references: [attachmentId])
    purchaseOrderId           String
    purchaseOrder             PurchaseOrder @relation(fields: [purchaseOrderId], references: [purchaseOrderId])
}
