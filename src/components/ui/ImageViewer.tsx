import { Paperclip } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { Button } from "./button";

const ImageViewer = ({
  h = 100,
  w = 100,
  onDel,
  className,
  attachment,
}: {
  h: number;
  w: number;
  onDel: () => void;
  className: string;
  attachment: {
    attachmentId?: string;
    url: string;
    key: string | null;
    name: string | null;
    type: string;
    createdAt?: Date;
    updatedAt?: Date;
  };
}) => {
  // console.log(attachment);

  return (
    <>
      {attachment.type.includes("image") ? (
        <Link
          title={attachment.name || ""}
          target="_blank"
          href={attachment.url}
          className=""
        >
          <div className="flex flex-col">
            <Image
              className={className}
              // onClick={() => setOpen(true)}
              alt={attachment.name || ""}
              width={w}
              height={h}
              src={attachment.url}
            />
            <small className="mx-auto text-xs text-gray-500">
              {attachment.name?.slice(0, 15) + "..."}
            </small>
          </div>
        </Link>
      ) : (
        <Link
          href={attachment.url}
          target="_blank"
          title={attachment.name || ""}
          rel="noopener noreferrer"
          className="group flex size-24 w-32 items-center justify-center rounded-md border border-input bg-background font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
        >
          <Paperclip
            size={30}
            className="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 group-hover:visible group-hover:absolute"
          />
          <p className="visible text-center text-xs group-hover:invisible">
            {attachment.name?.slice(0, 15) + "..."}
          </p>
        </Link>
      )}
    </>
  );
};

export default ImageViewer;
