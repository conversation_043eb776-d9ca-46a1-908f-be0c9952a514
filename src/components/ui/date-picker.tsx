"use client";

import * as React from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export function DatePicker({
  onSelect,
  date,
  disabled = false,
}: {
  onSelect: (date: Date) => void;
  date?: Date;
  disabled?: boolean;
}) {
  if (date)
    console.log(
      format(
        new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1),
        "PPP",
      ),
    );

  return (
    <Popover key={date?.toString()}>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
          )}
        >
          <CalendarIcon />
          {date ? (
            format(
              new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1),
              "PPP",
            )
          ) : (
            <span>Pick a date</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          disabled={disabled}
          fromDate={new Date()}
          mode="single"
          selected={
            date
              ? new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  date.getDate() - 1,
                )
              : undefined
          }
          onSelect={(selected) => {
            if (!selected) return;
            const localDate = new Date(
              selected.getFullYear(),
              selected.getMonth(),
              selected.getDate() + 1,
            );
            console.log(localDate);

            onSelect(localDate);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
