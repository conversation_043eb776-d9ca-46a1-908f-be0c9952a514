"use client";
import React from "react";
import { useChannel, useConnectionStateListener } from "ably/react";
import { UserI } from "@/lib/types";
import { toast } from "react-toastify";
import { Notification } from "@prisma/client";
import { useRouter } from "next/navigation";

const ClientProviders = ({ user }: { user: UserI }) => {
  const router = useRouter();

  useConnectionStateListener("connected", () => {
    console.log("Connected to Ably!");
  });

  useChannel("notifications", "po-notifi", (message) => {
    const { id, text, PurchaseOrderPaymentId, purchaseOrderId } = JSON.parse(
      message.data,
    ) as Notification;
    if (user.id === id)
      toast(text, {
        onClick: () => {
          // notification url
          router.push(
            text.includes("rejected")
              ? `/purchaseOrder/${purchaseOrderId}`
              : `/purchaseOrder/process/${purchaseOrderId}_${PurchaseOrderPaymentId}`,
          );
        },
        // autoClose: false,
      });
  });
  return (
    <div>
      <div></div>
    </div>
  );
};

export default ClientProviders;
