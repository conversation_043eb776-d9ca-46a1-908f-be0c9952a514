import React from "react";

export const NotificationSVG = () => {
  return (
    <svg
      viewBox="0 0 500 500"
      enable-background="new 0 0 500 500"
      id="Layer_1"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      fill="#000000"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        {" "}
        <g>
          {" "}
          <path
            d="M415.484,98.802c-32.513-34.721-79.476-7.76-80.147,35.672c-70.075,5.061-140.187,9.532-210.236,14.956 c-17.966,2.95-46.232-2.155-50.804,21.342c-1.24,60.734,2.622,123.439,6.731,184.645c3.626,53.981,13.508,66.33,68.775,60.224 c60.022-3.725,119.983-8.527,180.067-11.174c24.076-3.344,58.891,5.813,72.874-19.608c6.803-69.612-1.65-141.737-8.182-211.589 C422.206,159.684,437.387,125.278,415.484,98.802z"
            fill="#58454D"
          ></path>{" "}
          <path
            d="M77.043,200.22c0,0-5.898-37.514,9.685-43.032c15.582-5.518,252.151-20.042,252.151-20.042 c4.725,25.336,29.182,35.889,51.801,32.422v10.905L290.435,263.59l103.159,113.475c0,0,5.993,5.305,4.421,9.628 c-1.572,4.323-3.34,9.825-30.064,11.986c-26.723,2.161-240.453,18.059-255.29,14.639s-21.01-17.39-21.01-17.39 c31.542-38.981,90.779-95.622,122.754-128.713L77.043,200.22z"
            fill="#F8C795"
          ></path>{" "}
          <path
            d="M75.948,203.757c1.387,18.645,7.775,165.614,15.704,192.171c27.501-33.603,95.75-100.981,122.754-128.713 L82.009,202.643L75.948,203.757z"
            fill="#EEB378"
          ></path>{" "}
          <path
            d="M390.679,180.473c3.311,9.848,11.742,179.35,11.263,196.789c0.003-0.001-3.927,9.431-3.927,9.431 l-2.812-7.957c-24.81-27.197-79.607-87.453-104.769-115.146L390.679,180.473z"
            fill="#E79959"
          ></path>{" "}
          <path
            d="M78.493,207.617c14.01,11.338,97.763,32.809,86.448,70.775c-17.928,56.139-43.08,73.162-75.219,106.374 l2.5,10.278c27.972-35.156,90.453-95.77,121.743-128.044c-16.305-7.891-119.075-58.348-132.923-64.358 C81.042,202.643,78.322,204.844,78.493,207.617z"
            fill="#E79A59"
          ></path>{" "}
          <path
            d="M300.817,264.081c17.167-21.182,69.385-53.491,72.724,16.452c3.397,37.389,5.486,55.366,1.619,55.317 C371.292,335.801,299.115,270.172,300.817,264.081z"
            fill="#EEB378"
          ></path>{" "}
          <path
            d="M421.342,128.644c-0.903,52.885-79.569,52.877-80.464-0.002C341.781,75.759,420.447,75.767,421.342,128.644 z"
            fill="#EF7F5F"
          ></path>{" "}
          <path
            d="M361.306,165.467c13.541-28.613,71.728-24.507,43.817-69.473 C446.673,125.25,405.473,189.332,361.306,165.467z"
            fill="#E86042"
          ></path>{" "}
          <path
            d="M348.66,397.5c8.827-12.763,18.431-30.272,12.367-46.224c-2.145-5.547-7.057-10.448-7.144-16.586 C383.813,373.199,433.621,397.979,348.66,397.5z"
            fill="#F5B278"
          ></path>{" "}
          <path
            d="M382.211,184.244c-23.481-1.585-49.898-19.176-53.68-43.572c-0.063-1.503-1.163-0.848-1.857-1.063 c2.548,0.281,10.199-2.208,10.858,0.681c4.542,18.066,20.276,32.047,38.92,33.776c3.01,1.401,13.335-1.867,13.486,0.914 C391.776,180.301,385.478,181.433,382.211,184.244z"
            fill="#F5B278"
          ></path>{" "}
          <path
            d="M290.114,265.562c-43.59,29.075-16.187,30.127-68.243,6.095l0,0c0,0,0,0,0,0c0,0.001,0,0.002,0,0.003 c-1.939-1.313-4.193-1.915-6.217-3.038c-2.967-2.564-4.174,0.662-6.329,2.441c-3.573,3.393-7.276,6.795-10.67,10.423 c20.517-17.927,40.3,13.456,62.018,11.05c19.741-6.929,12.626-16.08,42.436-14.65C299.151,273.383,294.43,269.686,290.114,265.562z "
            fill="#F5B278"
          ></path>{" "}
          <path
            d="M80.44,208.491C80.44,208.491,80.44,208.491,80.44,208.491c0,0.01,0,0.019-0.001,0.029 c0.006-0.008,0.013-0.011,0.019-0.018C80.452,208.499,80.446,208.495,80.44,208.491z"
            fill="#58454D"
          ></path>{" "}
          <path
            d="M415.484,98.802c-32.513-34.721-79.476-7.76-80.147,35.672c-70.075,5.061-140.187,9.532-210.236,14.956 c-17.966,2.95-46.232-2.155-50.804,21.342c-1.24,60.734,2.622,123.439,6.731,184.645c3.626,53.981,13.508,66.33,68.775,60.224 c60.022-3.725,119.983-8.527,180.067-11.174c24.076-3.344,58.891,5.813,72.874-19.608c6.803-69.612-1.65-141.737-8.182-211.589 C422.206,159.684,437.387,125.278,415.484,98.802z M79.602,198.39c-3.948-33.069,1.459-43.322,36.173-42.84 c73.673-3.927,147.24-10.014,220.881-14.472c16.587,47.003,52.439,26.129,51.346,37.332 c-44.135,32.568-85.588,74.396-129.913,105.57C198.67,255.861,137.943,227.114,79.602,198.39z M92.817,390.914 c-0.665-2.144-0.924-4.199-1.113-6.267c0,0,0,0,0,0c-0.044-0.179-0.088-0.358-0.132-0.537c0,0,0,0,0,0 c-7.701-58.881-8.004-118.475-11.479-177.698c2.571-3.48,30.416,12.844,128.814,59.878 C176.105,306.977,127.444,347.332,92.817,390.914z M394.985,386.587c-12.031,14.276-42.04,7.655-59.379,10.049 c-52.967,1.642-105.775,6.131-158.588,10.288c-22.794-0.661-67.227,14.091-80.923-7.96c-2.497-3.576-2.422-3.553,0.396-6.917 c36.736-41.096,75.699-80.383,114.851-119.111c2.091-1.724,3.251-4.844,6.127-2.363c1.959,1.087,4.142,1.67,6.019,2.941 c0-0.001,0-0.002,0-0.003c0,0,0,0,0,0l0,0c43.69,19.163,26.357,25.786,64.567-5.968c24.75,18.45,45.124,46.642,66.973,69.602v0 C362.693,347.944,395.317,376.665,394.985,386.587z M395.966,289.77c-0.871,30.102,4.838,60.521,2.202,90.333 c-33.718-39.761-68.33-78.598-104.74-115.865c21.112-20.767,48.658-40.084,71.795-60.488c3.874-2.268,21.703-21.151,23.512-17.51 C391.789,220.692,394.85,255.194,395.966,289.77z M419.126,128.862c-8.366,69.663-108.682,31.433-65.848-25.297 C375.498,77.122,420.717,94.233,419.126,128.862z"
            fill="#58454D"
          ></path>{" "}
        </g>{" "}
      </g>
    </svg>
  );
};
