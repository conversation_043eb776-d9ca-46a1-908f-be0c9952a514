"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Building2,
  Command,
  Frame,
  GalleryVerticalEnd,
  LibraryBig,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  User,
  Users,
  PlusCircle,
  ReceiptText,
  LayoutDashboard, // New icon for "New Purchase Orders"
} from "lucide-react";

import { NavUser } from "@/components/nav-user";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useRouter } from "next/navigation";
import Image from "next/image";

import { UserI } from "@/lib/types";

// Sample data
const data = {
  "Purchase Orders": [
    {
      title: "Review Purchase Orders",
      url: "/",
      icon: GalleryVerticalEnd,
      isActive: true,
      access: ["*"],
    },
    {
      title: "My Purchase Orders",
      url: "/purchaseOrder",
      icon: ReceiptText,
      isActive: true,
      access: ["*"],
    },
    {
      title: "New Purchase Orders",
      url: "/purchaseOrder/new",
      icon: PlusCircle,
      isActive: false,
      access: ["*"],
    },
  ],
  Management: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
      access: ["admin", "ceo", "accountant", "accountantManager", "auditor"],
    },
    {
      title: "Users Management",
      url: "/manage/users",
      icon: Users,
      access: ["admin"],
    },
    {
      title: "Vendors Management",
      url: "/manage/companies",
      icon: Building2,
      access: ["admin", "accountant", "auditor"],
    },
    {
      title: "Projects Management",
      url: "/manage/projects",
      icon: LibraryBig,
      access: ["admin", "accountant", "auditor"],
    },
  ],
};

export function AppSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar> & { user: UserI }) {
  const { user } = props;
  const router = useRouter();

  // Helper function to determine if a menu item should be shown.
  const hasAccess = (item: { access?: string[] }) => {
    // If no access array is provided, show by default.
    if (!item?.access?.length) return true;
    // Show if the wildcard is provided or the user's role matches.
    return (
      item.access.includes("*") || item.access.includes(user.role.role || "")
    );
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu className="h-full">
          <div
            style={{
              width: "100%",
              position: "relative",
              aspectRatio: "180 / 50",
            }}
          >
            <Image
              src="/logo.jpeg"
              alt="logo"
              fill
              style={{ objectFit: "contain" }}
            />
          </div>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {Object.keys(data).map((group) => {
          // Filter out items where the user doesn't have access.
          const accessibleItems =
            data[group as keyof typeof data].filter(hasAccess);

          // If there are no accessible items in this group, don't render it.
          if (accessibleItems.length === 0) return null;

          return (
            <SidebarGroup key={group}>
              <SidebarGroupLabel>{group}</SidebarGroupLabel>
              <SidebarMenu>
                {accessibleItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild tooltip={item.title}>
                      <a href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          );
        })}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={props.user as any} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
