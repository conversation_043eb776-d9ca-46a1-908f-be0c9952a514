import {
  Currency,
  PaymentMethod,
  PaymentStatus,
  PurchaseOrderType,
} from "@prisma/client";
import z from "zod";

const itemSchema = z.object({
  purchaseOrderItemId: z.string().optional(),
  description: z.string().optional(),
  priceNoTax: z.number(),
  priceTax: z.number(),
  taxAmount: z.number(),
  purchaseOrderDetailId: z.string().optional(),
});
const installmentSchema = z.object({
  PurchaseOrderInstallmentId: z.string().optional(),
  description: z.string(),
  amount: z.number().min(0),
  percentage: z.number().min(0),
  date: z.date(),
  paid: z.boolean(),
  purchaseOrderDetailId: z.string().optional(),
});

export const createPOSchema = z.object({
  po: z.object({
    paymentType: z.enum([
      PurchaseOrderType.cash,
      PurchaseOrderType.installment,
    ]),
    currency: z.enum([
      Currency.AED,
      Currency.EUR,
      Currency.GBP,
      Currency.JOD,
      Currency.USD,
    ]),
    isDraft: z.boolean(),
    isPaid: z.boolean(),
    userApproveId: z.string(),
    userAuditId: z.string(),
    userReviewId: z.string(),
    paymentMethod: z.enum([
      PaymentMethod.CLIQ,
      PaymentMethod.bankTransfer,
      PaymentMethod.cash,
      PaymentMethod.cheque,
      PaymentMethod.eFAWATEERCOM,
    ]),
    contactName: z.string().default("").nullable(),
    contactNumber: z.string().default("").nullable(),
    projectId: z.string().optional(),
    companyId: z.string().optional(),
    description: z.string().optional().default("").nullable(),
    cliq: z.string().optional().nullable(),
    swiftCode: z.string().default("").nullable(),
    accountName: z.string().default("").nullable(),
    iban: z.string().optional().nullable(),
    nameOnCheque: z.string().optional().nullable(),
    identifier: z.string().optional().nullable(),
  }),
  totalAmount: z.number().min(1),
  // purchaseOrderDetailId: z.string().optional(),
  // purchaseOrderId: z.string().optional(),
  // date: z.date().optional().nullable(),

  // status: z.enum([
  //   Status.approved,
  //   Status.completed,
  //   Status.rejected,
  //   Status.paymnetInProgress,
  //   Status.toApprove,
  //   Status.toReview,
  //   Status.draft,
  // ]),

  // paid: z.boolean(),
  items: z
    .array(
      z.object({
        // purchaseOrderItemId: z.string().optional(),
        // purchaseOrderDetailId: z.string().optional(),
        taxAmount: z.number(),
        priceNoTax: z.number(),
        priceTax: z.number(),
        description: z.string().optional(),
      }),
    )
    // .optional()
    .default([]),
  payments: z
    .array(
      z.object({
        amount: z.number().min(0),
        date: z.date(),
        description: z.string(),
        percentage: z.number().min(0),
        status: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,
          PaymentStatus.transferred,
        ]),
      }),
    )
    .default([]),
  attachments: z.array(
    z.object({
      url: z.string(),
      appUrl: z.string(),
      customId: z.string().nullable(),
      type: z.string(),
      fileHash: z.string(),
      key: z.string(),
      name: z.string(),
      size: z.number(),
      lastModified: z.number(),
    }),
  ),
});

export const updatePOSchema = z.object({
  totalAmount: z.number().min(1),
  po: z.object({
    purchaseOrderDetailId: z.string(),
    referenceNumber: z.number(),
    purchaseOrderId: z.string(),
    paymentType: z.enum([
      PurchaseOrderType.cash,
      PurchaseOrderType.installment,
    ]),
    currency: z.enum([
      Currency.AED,
      Currency.EUR,
      Currency.GBP,
      Currency.JOD,
      Currency.USD,
    ]),
    isDraft: z.boolean(),
    isPaid: z.boolean(),
    userApproveId: z.string(),
    userAuditId: z.string(),
    userReviewId: z.string(),
    paymentMethod: z.enum([
      PaymentMethod.CLIQ,
      PaymentMethod.bankTransfer,
      PaymentMethod.cash,
      PaymentMethod.cheque,
      PaymentMethod.eFAWATEERCOM,
    ]),
    contactName: z.string().default("").nullable(),
    contactNumber: z.string().default("").nullable(),
    projectId: z.string().optional(),
    companyId: z.string().optional(),
    description: z.string().optional().default("").nullable(),
    cliq: z.string().optional().nullable(),
    swiftCode: z.string().default("").nullable(),
    accountName: z.string().default("").nullable(),
    iban: z.string().optional().nullable(),
    nameOnCheque: z.string().optional().nullable(),
    identifier: z.string().optional().nullable(),
  }),
  // totalAmount: z.number().min(1),
  // purchaseOrderDetailId: z.string().optional(),
  // purchaseOrderId: z.string().optional(),
  // date: z.date().optional().nullable(),

  // status: z.enum([
  //   Status.approved,
  //   Status.completed,
  //   Status.rejected,
  //   Status.paymnetInProgress,
  //   Status.toApprove,
  //   Status.toReview,
  //   Status.draft,
  // ]),

  // paid: z.boolean(),
  updatedItems: z
    .array(
      z.object({
        purchaseOrderItemId: z.string().optional(),
        purchaseOrderDetailId: z.string().optional(),
        taxAmount: z.number(),
        priceNoTax: z.number(),
        priceTax: z.number(),
        description: z.string().optional(),
      }),
    )
    // .optional()
    .default([]),
  newItems: z
    .array(
      z.object({
        purchaseOrderItemId: z.string().optional().nullable(),
        purchaseOrderDetailId: z.string().optional(),
        taxAmount: z.number(),
        priceNoTax: z.number(),
        priceTax: z.number(),
        description: z.string().optional(),
      }),
    )
    // .optional()
    .default([]),
  updatedPayments: z
    .array(
      z.object({
        PurchaseOrderPaymentId: z.string(),
        purchaseOrderDetailId: z.string(),
        amount: z.number().min(0),
        date: z.date(),
        description: z.string(),
        percentage: z.number().min(0),
        status: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,
          PaymentStatus.transferred,
        ]),
      }),
    )
    .default([]),
  newPayments: z
    .array(
      z.object({
        PurchaseOrderPaymentId: z.string().optional().nullable(),
        purchaseOrderDetailId: z.string().optional().nullable(),
        amount: z.number().min(0),
        date: z.date(),
        description: z.string(),
        percentage: z.number().min(0),
        status: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,

          PaymentStatus.transferred,
        ]),
      }),
    )
    .default([]),
});
export const sendToReviewSchema = z.object({
  purchaseOrderId: z.string(),
  userReviewId: z.string(),
  companyName: z.string(),
  projectName: z.string(),
  paymentType: z.enum([PurchaseOrderType.cash, PurchaseOrderType.installment]),
  payments: z
    .array(
      z.object({
        PurchaseOrderPaymentId: z.string(),
        purchaseOrderDetailId: z.string(),
        amount: z.number().min(0),
        date: z.date(),
        description: z.string(),
        percentage: z.number().min(0),
        status: z.enum([
          PaymentStatus.idle,
          PaymentStatus.paid,
          PaymentStatus.rejected,
          PaymentStatus.toApprove,
          PaymentStatus.toAudit,
          PaymentStatus.toPay,
          PaymentStatus.toReview,
          PaymentStatus.transferred,
        ]),
      }),
    )
    .default([]),
});

export type createI = z.infer<typeof createPOSchema>;
