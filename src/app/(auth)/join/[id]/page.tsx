"use client";
import { notFound, use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
// import { useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { api } from "@/trpc/react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "react-toastify";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const Page = () => {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const [form, setForm] = useState({
    username: "",
    password: "",
    email: "",
  });
  console.log(id);

  const { data: tempUser, isLoading } = api.user.getUserToJoin.useQuery({
    id,
  });
  const { mutate: registerMutations, isPending: loadReg } =
    api.user.inviteRegister.useMutation();
  console.log(tempUser);

  // if (!tempUser && !isLoading) {
  //   return notFound();
  // }

  const handleSubmit = async () => {
    registerMutations(
      {
        id,
        password: form.password,
        username: form.username,
      },
      {
        onSuccess: () => {
          toast.success("Registered Successfully");
          router.push("/login");
        },
      },
    );
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="max-w-sm">
        <CardHeader>
          <CardTitle>Setup Account</CardTitle>
          <CardDescription>Please enter your details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                disabled
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={tempUser?.email || ""}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="username">User name</Label>
              <Input
                id="username"
                type="text"
                placeholder="john doe"
                value={form.username}
                onChange={(e) => setForm({ ...form, username: e.target.value })}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>

              <Input
                id="password"
                type="password"
                required
                value={form.password}
                onChange={(e) => setForm({ ...form, password: e.target.value })}
              />
            </div>

            <Button
              //   disabled={createUser.isPending}
              type="submit"
              className="w-full"
              onClick={handleSubmit}
            >
              {loadReg ? (
                <Loader2 className="animate-spin" />
              ) : (
                "Register Information"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Page;
