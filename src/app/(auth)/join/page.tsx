"use client";
import { notFound, use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
// import { useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { api } from "@/trpc/react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "react-toastify";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const Page = () => {
  const router = useRouter();
  const [form, setForm] = useState({
    email: "",
  });

  const { mutate: getByEmail, isPending: loadReg } =
    api.user.getByEmail.useMutation();

  const handleCheck = async () => {
    getByEmail(
      {
        email: form.email,
      },
      {
        onSuccess: (u) => {
          if (u) {
            router.push("/join/" + u.id);
          } else {
            toast.error("Email NOT Found");
          }
        },
      },
    );
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="max-w-sm">
        <CardHeader>
          <CardTitle>Provide Your Email</CardTitle>
          <CardDescription>
            {/* Please enter your details to register */}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={form?.email || ""}
                onChange={(e) => setForm({ ...form, email: e.target.value })}
              />
            </div>

            <Button
              //   disabled={createUser.isPending}
              type="submit"
              className="w-full"
              onClick={handleCheck}
            >
              {loadReg ? <Loader2 className="animate-spin" /> : "Setup"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Page;
