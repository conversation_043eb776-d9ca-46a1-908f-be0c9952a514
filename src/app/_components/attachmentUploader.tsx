import React, { useEffect } from "react";
import { UploadButton } from "@/components/uploadThings";
import { ClientUploadedFileData } from "uploadthing/types";
import { api } from "@/trpc/react";

export const AttachmentUploader = ({
  attachments,
  setAttachments,
}: {
  attachments: ClientUploadedFileData<{ uploadedBy: string }>[];
  setAttachments: (
    attachments: ClientUploadedFileData<{ uploadedBy: string }>[],
  ) => void;
}) => {
  const { mutate: deleteAttachmentFromUT } =
    api.attachment.deleteFromUT.useMutation();
  const deleteAttachmentHandler = (keys: string[]) => {
    deleteAttachmentFromUT({
      keys: keys,
    });
  };
  useEffect(() => {
    const handleUnload = (e: any) => {
      if (attachments?.length) {
        e.preventDefault();
        console.log("unloading", attachments);
        deleteAttachmentHandler(
          attachments.map((attachment) => attachment.key),
        );
      }
    };

    if (attachments?.length) {
      window.addEventListener("beforeunload", handleUnload);
    }

    return () => {
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [attachments]);

  return (
    <UploadButton
      appearance={{
        allowedContent: {
          display: "none",
        },
      }}
      className="my-2"
      endpoint="imageUploader"
      config={{
        mode: "auto",
      }}
      onClientUploadComplete={(res) => {
        console.log("Files: ", res);
        setAttachments([...attachments, ...res]);
      }}
      onUploadError={(error: Error) => {
        alert(`ERROR! ${error.message}`);
      }}
    />
  );
};
