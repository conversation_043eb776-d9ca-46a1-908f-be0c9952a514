import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Loader } from "lucide-react";
import React from "react";

const CardLoader = ({ message }: { message?: string }) => {
  return (
    <div className="flex h-full w-full justify-center p-5">
      <Card className="h-fit w-full">
        <CardHeader className="flex justify-between">
          <CardTitle className="flex justify-between">
            <p className="animate-pulse">{message || "Loading Resources"}</p>
            <Loader size={30} className="animate-spin" />
          </CardTitle>
        </CardHeader>
      </Card>
    </div>
  );
};

export default CardLoader;
