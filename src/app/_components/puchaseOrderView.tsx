"use client";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, Paperclip, Printer, Image as ImageIcon } from "lucide-react";
import { PurchaseOrderI } from "@/lib/types";
import Image from "next/image";
import { toast } from "react-toastify";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import ImageViewer from "@/components/ui/ImageViewer";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
export default function PuchaseOrderView({
  purchaseOrder,
  PurchaseOrderPaymentId,
}: {
  purchaseOrder: PurchaseOrderI;
  PurchaseOrderPaymentId: string | undefined;
}) {
  const handlePrint = () => {
    window.print();
  };

  if (!purchaseOrder) {
    return <div>Loading purchase order...</div>;
  }

  const { PurchaseOrderDetails } = purchaseOrder;

  const DetailItem = ({
    name,
    value,
    cn,
  }: {
    name: string;
    value: string | null | undefined;
    cn?: string;
  }) => {
    if (!value) return null;
    return (
      <div className={cn || "p-1 print:p-0.5"}>
        <span className="font-bold">{name}:</span> {value}
      </div>
    );
  };

  return (
    <div className="min-h-[100dvh] bg-white">
      <div className="mx-auto max-w-4xl">
        {/* Print button - will be hidden when printing */}
        <div className="mb-6 flex justify-end gap-4 print:hidden">
          {purchaseOrder.purchaseOrderAttachments?.length ? (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant={"outline"} className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4" />
                  View Attachments
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>Attachments</DialogHeader>
                <DialogTitle className="flex items-center gap-2"></DialogTitle>
                <div className="h-fit max-h-96 overflow-y-scroll p-2">
                  {purchaseOrder.purchaseOrderAttachments.map((a, i) =>
                    a.Attachment.type.includes("image") ? (
                      <Link
                        key={i}
                        target="_blank"
                        href={a.Attachment.url}
                        className="mb-3"
                      >
                        <div className="mb-3 flex items-center gap-2 border border-dashed p-2">
                          <Image
                            className={"max-h-[100px] object-contain"}
                            // onClick={() => setOpen(true)}
                            alt={a.Attachment.name || ""}
                            width={100}
                            height={100}
                            src={a.Attachment.url}
                          />
                          <small className="mx-auto text-gray-500">
                            {a.Attachment.name}
                          </small>
                        </div>
                      </Link>
                    ) : (
                      <Link
                        href={a.Attachment.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group flex size-24 w-full items-center justify-center rounded-md border border-input bg-background font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                      >
                        <Paperclip size={15} className="mx-2" />
                        <p className="text-center text-sm">
                          {a.Attachment.name}
                        </p>
                      </Link>
                    ),
                  )}
                </div>
              </DialogContent>
            </Dialog>
          ) : null}
          <Button onClick={handlePrint} className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            Print Purchase Order
          </Button>
        </div>

        {/* Purchase Order Document */}
        <div className="print-color rounded-lg border border-gray-200 p-8 shadow-sm print:absolute print:left-0 print:top-0 print:w-full print:translate-y-0">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                PURCHASE ORDER
              </h1>
              <p className="text-gray-500">
                #{purchaseOrder.referenceNumber}
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(
                      String(purchaseOrder.referenceNumber),
                    );
                    toast.success("Reference number copied to clipboard");
                  }}
                  className="ml-2 print:hidden"
                  variant={"ghost"}
                  size={"sm"}
                >
                  <Copy className="h-2 w-2" />
                </Button>
              </p>
            </div>
            <div className="flex flex-col items-end text-xs">
              <Image
                src={"/logo.jpeg"}
                className="text-right"
                alt="Logo"
                width={150}
                height={150}
              />
              {/* <p className="text-gray-500">Amman, Jordan</p> */}
            </div>
          </div>
          <div className="flex w-full items-center justify-between">
            <p title={"created at date"} className="mt-1 text-gray-500">
              Date: {format(new Date(purchaseOrder.createdAt), "MMM dd, yyyy")}
            </p>
            <div className="flex items-center gap-2">
              <Badge className="uppercase">
                {purchaseOrder.PurchaseOrderDetails.currency}
              </Badge>

              <Badge className="bg-blue-500 capitalize">
                {purchaseOrder.paymentType == "cash"
                  ? "Single Payment"
                  : "Installment"}
              </Badge>

              <Badge
                className={`${purchaseOrder.isPaid ? "bg-green-500" : "bg-yellow-500"} capitalize`}
              >
                {purchaseOrder.isPaid ? "Paid" : "In Progress"}
              </Badge>
            </div>
          </div>
          {/* Vendor and Project Info */}

          <div className="my-4">
            <h3 className="mb-2 text-lg font-semibold text-gray-900">
              Details
            </h3>
            <div className="grid grid-cols-1 rounded-md border border-gray-200 bg-gray-50 p-4 md:grid-cols-2 print:grid-cols-2">
              <DetailItem
                name="Project Name"
                value={PurchaseOrderDetails.project.projectName}
              />
              <DetailItem
                name="Company Name"
                cn="hidden print:block"
                value={PurchaseOrderDetails.company.companyName}
              />

              <p className="print:hidden">
                <span className="font-medium">Company Name:</span>{" "}
                <Link
                  className="text-blue-400 hover:underline"
                  target="_blank"
                  href={`/view/vendor/${PurchaseOrderDetails.company?.companyId}`}
                >
                  {PurchaseOrderDetails.company?.companyName || "N/A"}
                </Link>
              </p>

              <DetailItem
                name="Company Legal Name"
                value={PurchaseOrderDetails.company.legalName}
              />

              <DetailItem
                name="Contact Name"
                value={PurchaseOrderDetails.contactName}
              />
              <DetailItem
                name="Contact Number"
                value={PurchaseOrderDetails.contactNumber}
              />
              <DetailItem
                name="Payment Method"
                value={
                  PurchaseOrderDetails.paymentMethod.charAt(0).toUpperCase() +
                  PurchaseOrderDetails.paymentMethod.slice(1)
                }
              />

              <DetailItem
                name="Name On Cheque"
                value={PurchaseOrderDetails.nameOnCheque}
              />
              <DetailItem
                name="Identifier"
                value={PurchaseOrderDetails.identifier}
              />
              <DetailItem
                name="Bank Name"
                value={PurchaseOrderDetails.nameOnCheque}
              />
              <DetailItem name="IBAN" value={PurchaseOrderDetails.iban} />
              <DetailItem
                name="Swift Code"
                value={PurchaseOrderDetails.swiftCode}
              />
              <DetailItem name="Cliq" value={PurchaseOrderDetails.cliq} />
              <hr className="col-span-2" />
              <DetailItem
                name="Description"
                value={PurchaseOrderDetails.description}
                cn="md:col-span-2 print:col-span-2"
              />
            </div>
          </div>

          {/* Items Table */}
          <div className="mb-8 print:text-xs">
            <h3 className="mb-2 text-lg font-semibold text-gray-900">Items</h3>
            <div className="overflow-x-auto print:overflow-visible">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Description
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Price (No Tax)
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Tax
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Price (After Tax)
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {PurchaseOrderDetails.PurchaseOrderItems.map((item) => (
                    <tr key={item.purchaseOrderItemId}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                        {item.description}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-900">
                        {PurchaseOrderDetails.currency}{" "}
                        {item.priceNoTax.toLocaleString("en-US", {
                          minimumFractionDigits: 3,
                          maximumFractionDigits: 3,
                        })}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-900">
                        {item.taxAmount !== 0
                          ? item.taxAmount > 0
                            ? `${item.taxAmount}% VAT`
                            : `${item.taxAmount}% WHT`
                          : "-"}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-900">
                        {PurchaseOrderDetails.currency}{" "}
                        {item.priceTax.toLocaleString("en-US", {
                          minimumFractionDigits: 3,
                          maximumFractionDigits: 3,
                        })}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  {PurchaseOrderDetails.PurchaseOrderItems.reduce(
                    (total, item) => {
                      if (item.taxAmount > 0) {
                        return total + item.priceTax - item.priceNoTax;
                      }
                      return 0;
                    },
                    0,
                  ) ? (
                    <tr>
                      <th
                        scope="row"
                        colSpan={3}
                        className="px-6 py-3 text-right text-sm font-medium text-gray-900"
                      >
                        VAT
                      </th>
                      <td className="whitespace-nowrap px-6 py-3 text-right text-sm font-medium text-gray-900">
                        {PurchaseOrderDetails.currency}{" "}
                        {PurchaseOrderDetails.PurchaseOrderItems.reduce(
                          (total, item) => {
                            if (item.taxAmount > 0) {
                              return total + item.priceTax - item.priceNoTax;
                            }
                            return 0;
                          },
                          0,
                        ).toLocaleString("en-US", {
                          minimumFractionDigits: 3,
                          maximumFractionDigits: 3,
                        })}
                      </td>
                    </tr>
                  ) : null}
                  {PurchaseOrderDetails.PurchaseOrderItems.reduce(
                    (total, item) => {
                      if (item.taxAmount < 0) {
                        return total + item.priceNoTax - item.priceTax;
                      }
                      return 0;
                    },
                    0,
                  ) ? (
                    <tr>
                      <th
                        scope="row"
                        colSpan={3}
                        className="px-6 py-3 text-right text-sm font-medium text-gray-900"
                      >
                        WHT
                      </th>
                      <td className="whitespace-nowrap px-6 py-3 text-right text-sm font-medium text-gray-900">
                        {PurchaseOrderDetails.currency}{" "}
                        {(
                          PurchaseOrderDetails.PurchaseOrderItems.reduce(
                            (total, item) => {
                              if (item.taxAmount > 0) {
                                return total + item.priceTax - item.priceNoTax;
                              }
                              return 0;
                            },
                            0,
                          ) * -1
                        ).toLocaleString("en-US", {
                          minimumFractionDigits: 3,
                          maximumFractionDigits: 3,
                        })}
                      </td>
                    </tr>
                  ) : null}
                  <tr>
                    <th
                      scope="row"
                      colSpan={3}
                      className="px-6 py-3 text-right text-sm font-medium text-gray-900"
                    >
                      Total Amount
                    </th>
                    <td className="whitespace-nowrap px-6 py-3 text-right text-sm font-medium text-gray-900">
                      {PurchaseOrderDetails.currency}{" "}
                      {PurchaseOrderDetails.totalAmount.toLocaleString(
                        "en-US",
                        {
                          minimumFractionDigits: 3,
                          maximumFractionDigits: 3,
                        },
                      )}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Payment Schedule for Installment */}
          {purchaseOrder.paymentType === "installment" && (
            <div className="mb-8">
              <h3 className="mb-2 text-lg font-semibold text-gray-900">
                Payment Schedule
              </h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        Description
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        Amount
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        Percentage
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        {purchaseOrder.isPaid ? "Paid At" : "Due Date"}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                      >
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {PurchaseOrderDetails.PurchaseOrderPayments.sort(
                      (a, b) => a.date.getTime() - b.date.getTime(),
                    ).map((payment) => (
                      <tr
                        key={payment.PurchaseOrderPaymentId}
                        className={
                          PurchaseOrderPaymentId ===
                          payment.PurchaseOrderPaymentId
                            ? "animate-pulse bg-amber-500/40 print:animate-none"
                            : payment.status === "paid"
                              ? "bg-green-50"
                              : payment.status === "idle"
                                ? "bg-gray-300/20 text-gray-400"
                                : "text-gray-900"
                        }
                      >
                        <td className="whitespace-nowrap px-6 py-4 text-sm">
                          {payment.description}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm">
                          {PurchaseOrderDetails.currency}{" "}
                          {payment.amount.toLocaleString("en-US", {
                            minimumFractionDigits: 3,
                            maximumFractionDigits: 3,
                          })}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm">
                          {payment.percentage}%
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm">
                          {format(
                            new Date(
                              purchaseOrder.isPaid
                                ? payment.paidAt || new Date()
                                : payment.date,
                            ),
                            "MMM dd, yyyy",
                          )}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <span
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              payment.status === "paid"
                                ? "bg-green-100 text-green-800"
                                : payment.status === "toReview"
                                  ? "bg-blue-100 text-blue-800"
                                  : payment.status === "toApprove"
                                    ? "bg-purple-100 text-purple-800"
                                    : payment.status === "toAudit"
                                      ? "bg-yellow-100 text-yellow-800"
                                      : payment.status === "toPay"
                                        ? "bg-orange-100 text-orange-800"
                                        : payment.status === "rejected"
                                          ? "bg-red-100 text-red-800"
                                          : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {payment.status.charAt(0).toUpperCase() +
                              payment.status.slice(1)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Approval Information cash */}
          {purchaseOrder.paymentType === "cash" && (
            <div className="">
              <h3 className="mb-2 text-lg font-semibold text-gray-900">
                Approval Information
              </h3>
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                  <p className="text-sm font-medium">Prepared By</p>
                  <p className="text-sm">
                    {purchaseOrder.userPrepare.username}
                  </p>
                </div>
                {!!purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments[0]
                  ?.reviewedAt && (
                  <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                    <p className="text-sm font-medium">Reviewed By</p>
                    <p className="text-sm">
                      {purchaseOrder.userReview.username}
                    </p>
                  </div>
                )}
                {!!purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments[0]
                  ?.approvedAt && (
                  <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                    <p className="text-sm font-medium">Approved By</p>
                    <p className="text-sm">
                      {purchaseOrder.userApprove.username}
                    </p>
                  </div>
                )}
                {!!purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments[0]
                  ?.auditedAt && (
                  <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                    <p className="text-sm font-medium">Audited By</p>
                    <p className="text-sm">
                      {purchaseOrder.userAudit.username}
                    </p>
                  </div>
                )}

                {!!purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments[0]
                  ?.paidAt && (
                  <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                    <p className="text-sm font-medium">Paid By</p>
                    <p className="text-sm">
                      {
                        purchaseOrder.PurchaseOrderDetails
                          .PurchaseOrderPayments[0].paidBy.username
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          {/* Approval Information cash */}
          {purchaseOrder.paymentType === "installment" && (
            <div className="">
              <h3 className="mb-2 text-lg font-semibold text-gray-900">
                Approval Information
              </h3>
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                  <p className="text-sm font-medium">Prepared By</p>
                  <p className="text-sm">
                    {purchaseOrder.userPrepare.username}
                  </p>
                </div>
                <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                  <p className="text-sm font-medium">Reviewed By</p>
                  <p className="text-sm">{purchaseOrder.userReview.username}</p>
                </div>
                <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                  <p className="text-sm font-medium">Approved By</p>
                  <p className="text-sm">
                    {purchaseOrder.userApprove.username}
                  </p>
                </div>
                {purchaseOrder.userAudit && (
                  <div className="h-18 flex-1 rounded-md border border-gray-200 bg-gray-50 p-2">
                    <p className="text-sm font-medium">Audited By</p>
                    <p className="text-sm">
                      {purchaseOrder.userAudit.username}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
