"use client";

import { useState } from "react";
import { api } from "@/trpc/react";
import { getServerAuthSession } from "@/server/auth";
import { signIn } from "next-auth/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

const LoginForm = () => {
  // const session = await getServerAuthSession();
  const router = useRouter();
  const [form, setForm] = useState({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const handleSubmit = async () => {
    if (loading) return;
    setLoading(true);
    setError(false);

    signIn("credentials", {
      ...form,
      callbackUrl: "/",
      redirect: false,
    }).then((res) => {
      if (res?.status === 401) {
        setError(true);
        setLoading(false);
      } else {
        router.push("/");
      }
    });
  };

  return (
    <Card className="max-w-sm">
      <CardHeader>
        <CardTitle className="text-2xl">Login</CardTitle>
        <CardDescription>
          Enter your email below to login to your account
          {error && (
            <>
              <br />
              <small className="text-red-500">
                Incorrect email or password
              </small>
            </>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={form.email}
              onChange={(e) => setForm({ ...form, email: e.target.value })}
              required
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">Password</Label>

            <Input
              placeholder="*********"
              id="password"
              type="password"
              required
              value={form.password}
              onChange={(e) => setForm({ ...form, password: e.target.value })}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSubmit();
                }
              }}
            />
          </div>
          <div className="flex">
            <Link className="underline" href={"/join"}>
              Forgot Password or new user?
            </Link>
          </div>
          <Button
            disabled={loading}
            type="submit"
            className="w-full"
            onClick={handleSubmit}
          >
            {loading ? <Loader2 className="animate-spin" /> : "Login"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoginForm;
