"use client";

import type React from "react";
import { useEffect, useState, useMemo, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/react";
import { format } from "date-fns";
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Search,
  X,
  Calendar,
  Building,
  Briefcase,
  CreditCard,
} from "lucide-react";
import { Eye, FileSpreadsheet, Columns } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenuCheckboxItem } from "@/components/ui/dropdown-menu";
import * as XLSX from "xlsx";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import type {
  Company,
  PaymentMethod,
  PaymentStatus,
  Project,
  PurchaseOrder,
  PurchaseOrderAttachment,
  PurchaseOrderDetails,
  PurchaseOrderPayment,
  PurchaseOrderType,
  User,
} from "@prisma/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import Link from "next/link";

const getStatusFromPurchaseOrder = (
  po: PurchaseOrder,
): PaymentStatus | "In progress" => {
  if (po.isPaid) return "paid";
  return "In progress"; // Default status, adjust based on your business logic
};

const statusLabels: Record<
  PaymentStatus | "In progress",
  { label: string; color: string }
> = {
  idle: { label: "Draft", color: "bg-gray-200 text-gray-800" },
  toReview: { label: "To Review", color: "bg-blue-100 text-blue-800" },
  toApprove: { label: "To Approve", color: "bg-purple-100 text-purple-800" },
  toAudit: { label: "To Audit", color: "bg-indigo-100 text-indigo-800" },
  toPay: { label: "To Pay", color: "bg-amber-100 text-amber-800" },
  transferred: { label: "Transferred", color: "bg-cyan-100 text-cyan-800" },
  rejected: { label: "Rejected", color: "bg-red-100 text-red-800" },
  paid: {
    label: "Paid",
    color:
      "bg-green-100 text-green-800 hover:bg-green-200 hover:text-green-900",
  },
  "In progress": { label: "In progress", color: "" },
};

const paymentTypeLabels: Record<PurchaseOrderType, string> = {
  cash: "Single Payment",
  installment: "Installments",
};

const paymentMethodLabels: Record<PaymentMethod, string> = {
  bankTransfer: "Bank Transfer",
  cheque: "Cheque",
  cash: "Cash",
  CLIQ: "CLIQ",
  eFAWATEERCOM: "eFAWATEERCOM",
};

const PurchaseOrderTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Parse URL query parameters
  const search = searchParams.get("search") || "";
  const dateFrom = searchParams.get("dateFrom") || "";
  const dateTo = searchParams.get("dateTo") || "";
  const projectId = searchParams.get("projectId")?.split(",") || "";
  const companyId = searchParams.get("companyId")?.split(",") || "";
  const paymentType = searchParams.get("paymentType")?.split(",") || "";
  const paymentMethod = searchParams.get("paymentMethod")?.split(",") || "";
  const page = Number.parseInt(searchParams.get("page") || "0", 10);
  const pageSize = Number.parseInt(searchParams.get("pageSize") || "10", 10);

  // Local state for filters – if "all" is passed, treat it as undefined
  const [searchValue, setSearchValue] = useState(search);
  const [dateFromValue, setDateFromValue] = useState<Date | undefined>(
    dateFrom ? new Date(dateFrom) : undefined,
  );
  const [dateToValue, setDateToValue] = useState<Date | undefined>(
    dateTo ? new Date(dateTo) : undefined,
  );
  const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>(
    projectId && projectId[0] !== "all" ? projectId : [],
  );
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<string[]>(
    companyId && companyId[0] !== "all" ? companyId : [],
  );
  const [selectedPaymentTypes, setSelectedPaymentTypes] = useState<
    PurchaseOrderType[]
  >(
    paymentType && paymentType[0] !== "all"
      ? (paymentType as PurchaseOrderType[])
      : [],
  );
  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState<
    PaymentMethod[]
  >(
    paymentMethod && paymentMethod[0] !== "all"
      ? (paymentMethod as PaymentMethod[])
      : [],
  );

  const [data, setData] = useState<{
    data: PurchaseOrderData[];
    total: number;
  }>();

  const allDataMutation = api.dashboard.getPOs.useMutation();
  const filteredMutation = api.dashboard.getPOs.useMutation({
    onSuccess: (res) => {
      setData(res);
    },
  });

  // Column visibility state
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>(
    {
      id: true,
      date: true,
      company: true,
      project: true,
      amount: true,
      paymentType: true,
      paymentMethod: true,
      status: true,
      preparedBy: true,
      reviewedBy: false,
      paidBy: false,
      description: false,
      currency: false,
      isPaid: false,
      contactName: false,
      contactNumber: false,
      nameOnCheque: false,
      identifier: false,
      iban: false,
      accountName: false,
      swiftCode: false,
      cliq: false,
      updatedAt: false,
      attachments: false,
    },
  );

  // Fetch projects and companies for filter dropdowns
  const { data: projects } = api.project.getAll.useQuery();
  const { data: companies } = api.company.getAll.useQuery();

  // State variables for pagination and sorting
  const [currentPage, setCurrentPage] = useState(page);
  const [itemsPerPage, setItemsPerPage] = useState(pageSize);
  const [currentSortField, setCurrentSortField] = useState("createdAt");
  const [currentSortOrder, setCurrentSortOrder] = useState<"asc" | "desc">(
    "desc",
  );

  const debounce = (fn: (...args: any[]) => void, delay = 500) => {
    let timeoutId: NodeJS.Timeout;

    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        fn(...args);
      }, delay);
    };
  };

  const debouncedSetSearchValue = useCallback(
    debounce((value: string) => setSearchValue(value), 500),
    [],
  );

  // Fetch purchase orders with filters
  useEffect(() => {
    filteredMutation.mutate({
      filter: {
        search: searchValue || undefined,
        dateRange:
          dateFromValue || dateToValue
            ? { from: dateFromValue, to: dateToValue }
            : undefined,
        projectId:
          selectedProjectIds.length > 0 ? selectedProjectIds : undefined,
        companyId:
          selectedCompanyIds.length > 0 ? selectedCompanyIds : undefined,
        paymentType:
          selectedPaymentTypes.length > 0 ? selectedPaymentTypes : undefined,
        paymentMethod:
          selectedPaymentMethods.length > 0
            ? selectedPaymentMethods
            : undefined,
      },
      pagination: {
        skip: currentPage * itemsPerPage,
        take: itemsPerPage,
      },
    });
  }, [searchParams]);

  console.dir(data, { depth: null });
  type PurchaseOrderData = PurchaseOrder & {
    PurchaseOrderDetails?:
      | (PurchaseOrderDetails & {
          PurchaseOrderPayments: (PurchaseOrderPayment & {
            paidBy: User | null;
          })[];
          company: Company;
          project: Project;
        })
      | null; // <-- Allow null values
    purchaseOrderAttachments: PurchaseOrderAttachment[];
    userPrepare: User;
    userReview: User;
    userApprove: User;
    userAudit: User;
  };

  // Handle sort toggle/change
  const handleSort = (field: string) => {
    if (currentSortField === field) {
      setCurrentSortOrder(currentSortOrder === "asc" ? "desc" : "asc");
    } else {
      setCurrentSortField(field);
      setCurrentSortOrder("asc");
    }
  };

  // Transform backend data to match the expected shape (map PurchaseOrderDetails to details)
  const purchaseOrders: PurchaseOrderData[] = useMemo(() => {
    if (!data?.data) return [];
    return data.data;
  }, [data]);

  const sortedPurchaseOrders = useMemo(() => {
    if (!purchaseOrders.length) return [];

    return [...purchaseOrders].sort((a, b) => {
      // Helper function to get nested property values
      const getValue = (obj: any, path: string) =>
        path.split(".").reduce((acc, key) => acc?.[key], obj);

      let fieldA = getValue(a, currentSortField);
      let fieldB = getValue(b, currentSortField);

      console.log(fieldA, fieldB, currentSortField);
      if (fieldA == null || fieldB == null) return 0; // Handle null values

      // Convert numeric strings to actual numbers
      if (!isNaN(fieldA) && !isNaN(fieldB)) {
        fieldA = Number(fieldA);
        fieldB = Number(fieldB);
      }

      if (typeof fieldA === "string" && typeof fieldB === "string") {
        return currentSortOrder === "asc"
          ? fieldA.localeCompare(fieldB)
          : fieldB.localeCompare(fieldA);
      }

      if (typeof fieldA === "number" && typeof fieldB === "number") {
        return currentSortOrder === "asc" ? fieldA - fieldB : fieldB - fieldA;
      }

      if (fieldA instanceof Date && fieldB instanceof Date) {
        return currentSortOrder === "asc"
          ? fieldA.getTime() - fieldB.getTime()
          : fieldB.getTime() - fieldA.getTime();
      }

      return 0;
    });
  }, [purchaseOrders, currentSortField, currentSortOrder]);

  // Update URL when filters or pagination change
  const updateUrl = useCallback(() => {
    const params = new URLSearchParams();

    if (searchValue) params.set("search", searchValue);
    if (dateFromValue) params.set("dateFrom", dateFromValue.toISOString());
    if (dateToValue) params.set("dateTo", dateToValue.toISOString());
    if (selectedProjectIds.length > 0)
      params.set("projectId", selectedProjectIds.join(","));
    if (selectedCompanyIds.length > 0)
      params.set("companyId", selectedCompanyIds.join(","));
    if (selectedPaymentTypes.length > 0)
      params.set("paymentType", selectedPaymentTypes.join(","));
    if (selectedPaymentMethods.length > 0)
      params.set("paymentMethod", selectedPaymentMethods.join(","));

    params.set("page", currentPage.toString());
    params.set("pageSize", itemsPerPage.toString());

    router.push(`?${params.toString()}`);
  }, [
    searchValue,
    dateFromValue,
    dateToValue,
    selectedProjectIds,
    selectedCompanyIds,
    selectedPaymentTypes,
    selectedPaymentMethods,
    currentPage,
    itemsPerPage,
    router,
  ]);

  // Reset filters to default values
  const resetFilters = useCallback(() => {
    setSearchValue("");
    setDateFromValue(undefined);
    setDateToValue(undefined);
    setSelectedProjectIds([]);
    setSelectedCompanyIds([]);
    setSelectedPaymentTypes([]);
    setSelectedPaymentMethods([]);
    setCurrentPage(0);
    setCurrentSortField("createdAt");
    setCurrentSortOrder("desc");
  }, []);

  // Update URL when pagination or sorting change
  useEffect(() => {
    updateUrl();
  }, [
    currentPage,
    itemsPerPage,
    currentSortField,
    currentSortOrder,
    updateUrl,
  ]);

  const totalItems = data?.total || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Render sort icon
  const renderSortIcon = (field: string) => {
    if (currentSortField !== field) {
      return <ChevronsUpDown className="ml-1 h-4 w-4" />;
    }
    return currentSortOrder === "asc" ? (
      <ChevronUp className="ml-1 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4" />
    );
  };

  const handleQueryAllData = async () => {
    const { data: allData } = await allDataMutation.mutateAsync({
      filter: {
        search: searchValue || undefined,
        dateRange:
          dateFromValue || dateToValue
            ? { from: dateFromValue, to: dateToValue }
            : undefined,
        projectId:
          selectedProjectIds.length > 0 ? selectedProjectIds : undefined,
        companyId:
          selectedCompanyIds.length > 0 ? selectedCompanyIds : undefined,
        paymentType:
          selectedPaymentTypes.length > 0 ? selectedPaymentTypes : undefined,
        paymentMethod:
          selectedPaymentMethods.length > 0
            ? selectedPaymentMethods
            : undefined,
      },
      pagination: {
        skip: 0,
        take: *********, // Get a large number of records
      },
    });

    return allData;
  };

  // Export to Excel function
  const exportToExcel = async (exportAll: boolean) => {
    let dataToExport = sortedPurchaseOrders;

    if (exportAll) {
      // Fetch all data without pagination
      const allData = await handleQueryAllData();
      dataToExport = (allData || []) as PurchaseOrderData[];
    }

    // Transform data for Excel
    const excelData = dataToExport?.map((po) => ({
      ID: po.referenceNumber,
      Date: format(new Date(po.createdAt), "MMM dd, yyyy"),
      Company: po.PurchaseOrderDetails?.company?.companyName || "N/A",
      Project: po.PurchaseOrderDetails?.project?.projectName || "N/A",
      Currency: po.PurchaseOrderDetails?.currency || "N/A",
      "Total Amount": po.PurchaseOrderDetails
        ? `${po.PurchaseOrderDetails.totalAmount}`
        : "N/A",
      "Paid Amount": po.PurchaseOrderDetails
        ? `${po.PurchaseOrderDetails.PurchaseOrderPayments.reduce(
            (acc, payment) => acc + (payment.paidAt ? payment.amount || 0 : 0),
            0,
          )}`
        : 0,
      "Payment Type": paymentTypeLabels[po.paymentType] || "N/A",
      "Payment Method": po.PurchaseOrderDetails?.paymentMethod
        ? paymentMethodLabels[po.PurchaseOrderDetails.paymentMethod]
        : "N/A",
      Status: statusLabels[getStatusFromPurchaseOrder(po)].label,
      Description: po.PurchaseOrderDetails?.description || "N/A",
      "Is Paid": po.isPaid ? "Yes" : "No",
      "Contact Name": po.PurchaseOrderDetails?.contactName || "N/A",
      "Contact Number": po.PurchaseOrderDetails?.contactNumber || "N/A",
      "Name On Cheque": po.PurchaseOrderDetails?.nameOnCheque || "N/A",
      Identifier: po.PurchaseOrderDetails?.identifier || "N/A",
      IBAN: po.PurchaseOrderDetails?.iban || "N/A",
      "Account Name": po.PurchaseOrderDetails?.accountName || "N/A",
      "Swift Code": po.PurchaseOrderDetails?.swiftCode || "N/A",
      Cliq: po.PurchaseOrderDetails?.cliq || "N/A",
      "Prepared By": po.userPrepare?.username || "N/A",
      "Reviewed By": po.userReview?.username || "N/A",
      "Paid By":
        [
          ...new Set(
            po.PurchaseOrderDetails?.PurchaseOrderPayments?.map(
              (payment) => payment.paidBy?.username,
            ),
          ),
        ].join(", ") || "N/A",
      "Updated At": format(new Date(po.updatedAt), "MMM dd, yyyy"),
      Attachments: po.purchaseOrderAttachments?.length || 0,
    }));

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Purchase Orders");

    // Generate Excel file and trigger download
    XLSX.writeFile(wb, "purchase-orders.xlsx");
  };

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <CardTitle>Purchase Orders</CardTitle>
        <CardDescription>Manage and view all purchase orders</CardDescription>
      </CardHeader>
      <CardContent className="overflow-hidden p-4">
        {/* Filters */}
        <div className="mb-6 space-y-4">
          <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search purchase orders..."
                // value={searchValue}
                onChange={(e) => debouncedSetSearchValue(e.target.value)}
                className="pl-8"
              />
            </div>

            {/* Date Range */}
            <div className="flex space-x-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {dateFromValue ? format(dateFromValue, "PP") : "From Date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={dateFromValue}
                    onSelect={setDateFromValue}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {dateToValue ? format(dateToValue, "PP") : "To Date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={dateToValue}
                    onSelect={setDateToValue}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
            {/* Project Filter - Multi-select */}
            <div className="flex-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <Briefcase className="mr-2 h-4 w-4" />
                    {selectedProjectIds.length === 0
                      ? "All Projects"
                      : selectedProjectIds.length === 1
                        ? `${selectedProjectIds.length} Project`
                        : `${selectedProjectIds.length} Projects`}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="z-50 w-full p-0" align="start">
                  <div className="p-2">
                    <div className="mb-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setSelectedProjectIds([])}
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() =>
                          setSelectedProjectIds(
                            projects?.map((p) => p.projectId) || [],
                          )
                        }
                      >
                        Select All
                      </Button>
                    </div>
                    <div className="max-h-[200px] overflow-auto">
                      {projects?.map((project) => (
                        <div
                          key={project.projectId}
                          className="flex items-center space-x-2 p-2"
                        >
                          <Checkbox
                            id={`project-${project.projectId}`}
                            checked={selectedProjectIds.includes(
                              project.projectId,
                            )}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedProjectIds([
                                  ...selectedProjectIds,
                                  project.projectId,
                                ]);
                              } else {
                                setSelectedProjectIds(
                                  selectedProjectIds.filter(
                                    (id) => id !== project.projectId,
                                  ),
                                );
                              }
                            }}
                          />
                          <label
                            htmlFor={`project-${project.projectId}`}
                            className="flex-grow cursor-pointer text-sm"
                          >
                            {project.projectName}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Company Filter - Multi-select */}
            <div className="flex-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <Building className="mr-2 h-4 w-4" />
                    {selectedCompanyIds.length === 0
                      ? "All Companies"
                      : selectedCompanyIds.length === 1
                        ? `${selectedCompanyIds.length} Company`
                        : `${selectedCompanyIds.length} Companies`}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="z-50 w-full p-0" align="start">
                  <div className="p-2">
                    <div className="mb-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setSelectedCompanyIds([])}
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() =>
                          setSelectedCompanyIds(
                            companies?.map((c) => c.companyId) || [],
                          )
                        }
                      >
                        Select All
                      </Button>
                    </div>
                    <div className="max-h-[200px] overflow-auto">
                      {companies?.map((company) => (
                        <div
                          key={company.companyId}
                          className="flex items-center space-x-2 p-2"
                        >
                          <Checkbox
                            id={`company-${company.companyId}`}
                            checked={selectedCompanyIds.includes(
                              company.companyId,
                            )}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCompanyIds([
                                  ...selectedCompanyIds,
                                  company.companyId,
                                ]);
                              } else {
                                setSelectedCompanyIds(
                                  selectedCompanyIds.filter(
                                    (id) => id !== company.companyId,
                                  ),
                                );
                              }
                            }}
                          />
                          <label
                            htmlFor={`company-${company.companyId}`}
                            className="flex-grow cursor-pointer text-sm"
                          >
                            {company.companyName}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Payment Type Filter - Multi-select */}
            <div className="flex-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <CreditCard className="mr-2 h-4 w-4" />
                    {selectedPaymentTypes.length === 0
                      ? "All Payment Types"
                      : selectedPaymentTypes.length === 1
                        ? `${selectedPaymentTypes.length} Type`
                        : `${selectedPaymentTypes.length} Types`}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="z-50 w-full p-0" align="start">
                  <div className="p-2">
                    <div className="mb-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setSelectedPaymentTypes([])}
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() =>
                          setSelectedPaymentTypes(
                            Object.keys(
                              paymentTypeLabels,
                            ) as PurchaseOrderType[],
                          )
                        }
                      >
                        Select All
                      </Button>
                    </div>
                    <div className="max-h-[200px] overflow-auto">
                      {Object.entries(paymentTypeLabels).map(
                        ([type, label]) => (
                          <div
                            key={type}
                            className="flex items-center space-x-2 p-2"
                          >
                            <Checkbox
                              id={`type-${type}`}
                              checked={selectedPaymentTypes.includes(
                                type as PurchaseOrderType,
                              )}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedPaymentTypes([
                                    ...selectedPaymentTypes,
                                    type as PurchaseOrderType,
                                  ]);
                                } else {
                                  setSelectedPaymentTypes(
                                    selectedPaymentTypes.filter(
                                      (t) => t !== type,
                                    ),
                                  );
                                }
                              }}
                            />
                            <label
                              htmlFor={`type-${type}`}
                              className="flex-grow cursor-pointer text-sm"
                            >
                              {label}
                            </label>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Payment Method Filter - Multi-select */}
            <div className="flex-1">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <CreditCard className="mr-2 h-4 w-4" />
                    {selectedPaymentMethods.length === 0
                      ? "All Payment Methods"
                      : selectedPaymentMethods.length === 1
                        ? `${selectedPaymentMethods.length} Method`
                        : `${selectedPaymentMethods.length} Methods`}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="z-50 w-full p-0" align="start">
                  <div className="p-2">
                    <div className="mb-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() => setSelectedPaymentMethods([])}
                      >
                        Clear All
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={() =>
                          setSelectedPaymentMethods(
                            Object.keys(paymentMethodLabels) as PaymentMethod[],
                          )
                        }
                      >
                        Select All
                      </Button>
                    </div>
                    <div className="max-h-[200px] overflow-auto">
                      {Object.entries(paymentMethodLabels).map(
                        ([method, label]) => (
                          <div
                            key={method}
                            className="flex items-center space-x-2 p-2"
                          >
                            <Checkbox
                              id={`method-${method}`}
                              checked={selectedPaymentMethods.includes(
                                method as PaymentMethod,
                              )}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedPaymentMethods([
                                    ...selectedPaymentMethods,
                                    method as PaymentMethod,
                                  ]);
                                } else {
                                  setSelectedPaymentMethods(
                                    selectedPaymentMethods.filter(
                                      (m) => m !== method,
                                    ),
                                  );
                                }
                              }}
                            />
                            <label
                              htmlFor={`method-${method}`}
                              className="flex-grow cursor-pointer text-sm"
                            >
                              {label}
                            </label>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={resetFilters}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Reset Filters
            </Button>

            <div className="flex gap-2">
              {/* Column Visibility */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Columns className="h-4 w-4" />
                    Columns
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="max-h-[400px] overflow-y-auto"
                >
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.id}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, id: checked })
                    }
                  >
                    ID
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.date}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, date: checked })
                    }
                  >
                    Date
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.company}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, company: checked })
                    }
                  >
                    Company
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.project}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, project: checked })
                    }
                  >
                    Project
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.amount}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, amount: checked })
                    }
                  >
                    Amount
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.paymentType}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        paymentType: checked,
                      })
                    }
                  >
                    Payment Type
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.paymentMethod}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        paymentMethod: checked,
                      })
                    }
                  >
                    Payment Method
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.status}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, status: checked })
                    }
                  >
                    Status
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.description}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        description: checked,
                      })
                    }
                  >
                    Description
                  </DropdownMenuCheckboxItem>

                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.isPaid}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, isPaid: checked })
                    }
                  >
                    Is Paid
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.contactName}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        contactName: checked,
                      })
                    }
                  >
                    Contact Name
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.contactNumber}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        contactNumber: checked,
                      })
                    }
                  >
                    Contact Number
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.nameOnCheque}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        nameOnCheque: checked,
                      })
                    }
                  >
                    Name On Cheque
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.identifier}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        identifier: checked,
                      })
                    }
                  >
                    Identifier
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.iban}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, iban: checked })
                    }
                  >
                    IBAN
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.accountName}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        accountName: checked,
                      })
                    }
                  >
                    Account Name
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.swiftCode}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        swiftCode: checked,
                      })
                    }
                  >
                    Swift Code
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.cliq}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ ...visibleColumns, cliq: checked })
                    }
                  >
                    Cliq
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.preparedBy}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        preparedBy: checked,
                      })
                    }
                  >
                    Prepared By
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.reviewedBy}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        reviewedBy: checked,
                      })
                    }
                  >
                    Reviewed By
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.paidBy}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        paidBy: checked,
                      })
                    }
                  >
                    Paid By
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.updatedAt}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        updatedAt: checked,
                      })
                    }
                  >
                    Updated At
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={visibleColumns.attachments}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({
                        ...visibleColumns,
                        attachments: checked,
                      })
                    }
                  >
                    Attachments
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    Export
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Export Purchase Orders</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to export the purchase orders?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => {
                        exportToExcel(true);
                      }}
                    >
                      Export
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="w-full overflow-x-auto rounded-md border">
          <div className="min-w-full max-w-prose">
            <Table>
              <TableHeader>
                <TableRow>
                  {visibleColumns.id && (
                    <TableHead
                      className="sticky left-0 cursor-pointer border-b bg-white hover:bg-muted/50"
                      onClick={() => handleSort("referenceNumber")}
                    >
                      <div className="flex items-center">
                        Ref No. {renderSortIcon("referenceNumber")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.date && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("createdAt")}
                    >
                      <div className="flex items-center">
                        Date {renderSortIcon("createdAt")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.company && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.company.companyName")
                      }
                    >
                      <div className="flex items-center">
                        Company{" "}
                        {renderSortIcon(
                          "PurchaseOrderDetails.company.companyName",
                        )}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.project && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.project.projectName")
                      }
                    >
                      <div className="flex items-center">
                        Project{" "}
                        {renderSortIcon(
                          "PurchaseOrderDetails.project.projectName",
                        )}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.amount && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.totalAmount")
                      }
                    >
                      <div className="flex items-center">
                        Amount{" "}
                        {renderSortIcon("PurchaseOrderDetails.totalAmount")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.paymentType && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("paymentType")}
                    >
                      <div className="flex items-center">
                        Payment Type {renderSortIcon("paymentType")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.paymentMethod && (
                    <TableHead
                      className="cursor-pointer"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.paymentMethod")
                      }
                    >
                      <div className="flex items-center">
                        Payment Method{" "}
                        {renderSortIcon("PurchaseOrderDetails.paymentMethod")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.status && (
                    <TableHead className="cursor-pointer">
                      <div className="flex items-center">Status</div>
                    </TableHead>
                  )}
                  {visibleColumns.description && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.description")
                      }
                    >
                      <div className="flex items-center">
                        Description{" "}
                        {renderSortIcon("PurchaseOrderDetails.description")}
                      </div>
                    </TableHead>
                  )}

                  {visibleColumns.isPaid && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() => handleSort("isPaid")}
                    >
                      <div className="flex items-center">
                        Is Paid {renderSortIcon("isPaid")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.contactName && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.contactName")
                      }
                    >
                      <div className="flex items-center">
                        Contact Name{" "}
                        {renderSortIcon("PurchaseOrderDetails.contactName")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.contactNumber && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.contactNumber")
                      }
                    >
                      <div className="flex items-center">
                        Contact Number{" "}
                        {renderSortIcon("PurchaseOrderDetails.contactNumber")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.nameOnCheque && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.nameOnCheque")
                      }
                    >
                      <div className="flex items-center">
                        Name On Cheque{" "}
                        {renderSortIcon("PurchaseOrderDetails.nameOnCheque")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.identifier && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.identifier")
                      }
                    >
                      <div className="flex items-center">
                        Identifier{" "}
                        {renderSortIcon("PurchaseOrderDetails.identifier")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.iban && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() => handleSort("PurchaseOrderDetails.iban")}
                    >
                      <div className="flex items-center">
                        IBAN {renderSortIcon("PurchaseOrderDetails.iban")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.accountName && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.accountName")
                      }
                    >
                      <div className="flex items-center">
                        Account Name{" "}
                        {renderSortIcon("PurchaseOrderDetails.accountName")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.swiftCode && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() =>
                        handleSort("PurchaseOrderDetails.swiftCode")
                      }
                    >
                      <div className="flex items-center">
                        Swift Code{" "}
                        {renderSortIcon("PurchaseOrderDetails.swiftCode")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.cliq && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() => handleSort("PurchaseOrderDetails.cliq")}
                    >
                      <div className="flex items-center">
                        Cliq {renderSortIcon("PurchaseOrderDetails.cliq")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.preparedBy && (
                    <TableHead className="whitespace-nowrap">
                      Prepared By
                    </TableHead>
                  )}
                  {visibleColumns.reviewedBy && (
                    <TableHead className="whitespace-nowrap">
                      Reviewed By
                    </TableHead>
                  )}

                  {visibleColumns.paidBy && (
                    <TableHead className="whitespace-nowrap">Paid By</TableHead>
                  )}
                  {visibleColumns.updatedAt && (
                    <TableHead
                      className="cursor-pointer whitespace-nowrap"
                      onClick={() => handleSort("updatedAt")}
                    >
                      <div className="flex items-center">
                        Updated At {renderSortIcon("updatedAt")}
                      </div>
                    </TableHead>
                  )}
                  {visibleColumns.attachments && (
                    <TableHead className="whitespace-nowrap">
                      Attachments
                    </TableHead>
                  )}
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMutation.isPending ? (
                  Array.from({ length: itemsPerPage }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      {Array.from({
                        length:
                          Object.values(visibleColumns).filter(Boolean).length +
                          1,
                      }).map((_, cellIndex) => (
                        <TableCell key={`cell-${index}-${cellIndex}`}>
                          <Skeleton className="h-6 w-full" />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : sortedPurchaseOrders.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={
                        Object.values(visibleColumns).filter(Boolean).length + 1
                      }
                      className="h-24 text-center"
                    >
                      No purchase orders found.
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedPurchaseOrders.map((po) => {
                    const statusValue = getStatusFromPurchaseOrder(po);
                    const statusInfo = statusLabels[statusValue];
                    return (
                      <TableRow key={po.purchaseOrderId}>
                        {visibleColumns.id && (
                          <TableCell
                            className="sticky left-0 whitespace-nowrap bg-white font-medium"
                            title={po.purchaseOrderId}
                          >
                            {po.referenceNumber}
                            {/* {po.purchaseOrderId.substring(0,8)}... */}
                          </TableCell>
                        )}
                        {visibleColumns.date && (
                          <TableCell className="whitespace-nowrap">
                            {format(new Date(po.createdAt), "MMM dd, yyyy")}
                          </TableCell>
                        )}
                        {visibleColumns.company && (
                          <TableCell
                            className={`${po.PurchaseOrderDetails?.company?.companyId ? "cursor-pointer" : ""} whitespace-nowrap`}
                          >
                            <Link
                              target="_blank"
                              href={`/view/vendor/${po.PurchaseOrderDetails?.company?.companyId}`}
                            >
                              {po.PurchaseOrderDetails?.company.companyName ||
                                "N/A"}
                            </Link>
                          </TableCell>
                        )}
                        {visibleColumns.project && (
                          <TableCell className="whitespace-nowrap">
                            {po.PurchaseOrderDetails?.project.projectName ||
                              "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.amount && (
                          <TableCell>
                            {po.PurchaseOrderDetails ? (
                              <span>
                                {new Intl.NumberFormat("en-US", {
                                  style: "currency",
                                  currency: po.PurchaseOrderDetails.currency,
                                }).format(po.PurchaseOrderDetails.totalAmount)}
                              </span>
                            ) : (
                              "N/A"
                            )}
                          </TableCell>
                        )}
                        {visibleColumns.paymentType && (
                          <TableCell className="whitespace-nowrap">
                            {paymentTypeLabels[po.paymentType] === "Cash"
                              ? "Single Payment"
                              : paymentTypeLabels[po.paymentType]}
                          </TableCell>
                        )}
                        {visibleColumns.paymentMethod && (
                          <TableCell className="whitespace-nowrap">
                            {po.PurchaseOrderDetails?.paymentMethod
                              ? paymentMethodLabels[
                                  po.PurchaseOrderDetails?.paymentMethod
                                ]
                              : "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.status && (
                          <TableCell>
                            <Badge
                              className={
                                statusInfo.color + " whitespace-nowrap"
                              }
                            >
                              {statusInfo.label}
                            </Badge>
                          </TableCell>
                        )}

                        {visibleColumns.description && (
                          <TableCell
                            className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                            title={
                              po.PurchaseOrderDetails?.description || "N/A"
                            }
                          >
                            {po.PurchaseOrderDetails?.description || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.isPaid && (
                          <TableCell>{po.isPaid ? "Yes" : "No"}</TableCell>
                        )}
                        {visibleColumns.contactName && (
                          <TableCell
                            className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                            title={
                              po.PurchaseOrderDetails?.contactName || "N/A"
                            }
                          >
                            {po.PurchaseOrderDetails?.contactName || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.contactNumber && (
                          <TableCell>
                            {po.PurchaseOrderDetails?.contactNumber || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.nameOnCheque && (
                          <TableCell
                            className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                            title={
                              po.PurchaseOrderDetails?.nameOnCheque || "N/A"
                            }
                          >
                            {po.PurchaseOrderDetails?.nameOnCheque || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.identifier && (
                          <TableCell>
                            {po.PurchaseOrderDetails?.identifier || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.iban && (
                          <TableCell
                            className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                            title={po.PurchaseOrderDetails?.iban || "N/A"}
                          >
                            {po.PurchaseOrderDetails?.iban || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.accountName && (
                          <TableCell
                            className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                            title={
                              po.PurchaseOrderDetails?.accountName || "N/A"
                            }
                          >
                            {po.PurchaseOrderDetails?.accountName || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.swiftCode && (
                          <TableCell>
                            {po.PurchaseOrderDetails?.swiftCode || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.cliq && (
                          <TableCell>
                            {po.PurchaseOrderDetails?.cliq || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.preparedBy && (
                          <TableCell>{po.userPrepare.username}</TableCell>
                        )}
                        {visibleColumns.reviewedBy && (
                          <TableCell>{po.userReview.username}</TableCell>
                        )}

                        {visibleColumns.paidBy && (
                          <TableCell>
                            {[
                              ...new Set(
                                po.PurchaseOrderDetails?.PurchaseOrderPayments?.map(
                                  (payment) => payment.paidBy?.username,
                                ),
                              ),
                            ].join(", ") || "N/A"}
                          </TableCell>
                        )}
                        {visibleColumns.updatedAt && (
                          <TableCell>
                            {format(new Date(po.updatedAt), "MMM dd, yyyy")}
                          </TableCell>
                        )}
                        {visibleColumns.attachments && (
                          <TableCell>
                            {po.purchaseOrderAttachments?.length || 0}
                          </TableCell>
                        )}
                        <TableCell>
                          <Link
                            href={`/purchaseOrder/process/${po.purchaseOrderId}`}
                            target="_blank"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">View details</span>
                          </Link>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {sortedPurchaseOrders.length} of {totalItems} results
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={currentPage === 0}
            >
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageToShow;
                if (totalPages <= 5) {
                  pageToShow = i;
                } else if (currentPage < 2) {
                  pageToShow = i;
                } else if (currentPage > totalPages - 3) {
                  pageToShow = totalPages - 5 + i;
                } else {
                  pageToShow = currentPage - 2 + i;
                }
                return (
                  <Button
                    key={pageToShow}
                    variant={currentPage === pageToShow ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageToShow)}
                  >
                    {pageToShow + 1}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentPage(Math.min(totalPages - 1, currentPage + 1))
              }
              disabled={currentPage >= totalPages - 1}
            >
              Next
            </Button>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={(value) => {
                setItemsPerPage(Number.parseInt(value, 10));
                setCurrentPage(0); // Reset page to first when page size changes
              }}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="10 per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 per page</SelectItem>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="20">20 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PurchaseOrderTable;
