import { Label } from "@/components/ui/label";
import React from "react";
import Select from "react-select";

interface SelectCreateI {
  value: { value: string; label: string };
  onChange: (value: { value: string; label: string }) => void;
  placeholder: string;
  menuIsOpen?: boolean;
  disabled?: boolean;
  create?: (payload: string) => void;
  options: { value: string; label: string }[];
  label?: string;
  extraComponent?: React.ReactNode;
  noOptionsMessage?: (obj: { inputValue: string }) => React.ReactNode;
}
const SelectCreate = ({
  value,
  onChange,
  extraComponent,
  placeholder,
  create,
  label,
  options,
  noOptionsMessage,
  menuIsOpen,
  disabled,
}: SelectCreateI) => {
  console.log(value);

  return (
    <div>
      <Label className="mb-2 flex items-center gap-1">
        {label || ""} {extraComponent}
      </Label>
      <Select
        isDisabled={disabled}
        menuIsOpen={menuIsOpen}
        noOptionsMessage={noOptionsMessage}
        id={placeholder}
        placeholder={placeholder}
        value={Object.values(value).some((val) => !val) ? null : value}
        onChange={onChange as any}
        onKeyDown={(e: any) => {
          if (create && e.key === "Enter") {
            create(e.target.value);
          }
        }}
        options={options}
      />
    </div>
  );
};

export default SelectCreate;
