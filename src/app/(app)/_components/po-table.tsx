"use client";

import * as React from "react";
import { useState, useMemo } from "react";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  HoverCard,
  HoverCardTrigger,
  HoverCardContent,
} from "@/components/ui/hover-card";
import { PurchaseOrderI } from "@/lib/types";
import Link from "next/link";
import { useRouter } from "next/navigation";

export function PoTable({
  purchaseOrders,
}: {
  purchaseOrders: PurchaseOrderI[];
}) {
  const router = useRouter();

  // Single search input for filtering on project, vendor and reference number
  const [searchTerm, setSearchTerm] = useState("");

  // Sorting state
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter the purchase orders based on search term
  const filteredOrders = useMemo(() => {
    return purchaseOrders.filter((po) => {
      const projectName =
        po.PurchaseOrderDetails?.project?.projectName.toLowerCase() || "";
      const vendorName =
        po.PurchaseOrderDetails?.company?.companyName.toLowerCase() || "";
      const reference = String(po.referenceNumber).toLowerCase();
      const term = searchTerm.toLowerCase();
      return (
        projectName.includes(term) ||
        vendorName.includes(term) ||
        reference.includes(term) ||
        po?.PurchaseOrderDetails?.totalAmount?.toString().includes(term)
      );
    });
  }, [purchaseOrders, searchTerm]);

  // Sort filtered orders based on sortField and sortOrder
  const sortedOrders = useMemo(() => {
    if (!sortField) return filteredOrders;
    const sorted = [...filteredOrders].sort((a, b) => {
      let aVal = "";
      let bVal = "";

      if (sortField === "project") {
        aVal = a.PurchaseOrderDetails?.project?.projectName || "";
        bVal = b.PurchaseOrderDetails?.project?.projectName || "";
      } else if (sortField === "vendor") {
        aVal = a.PurchaseOrderDetails?.company?.companyName || "";
        bVal = b.PurchaseOrderDetails?.company?.companyName || "";
      } else if (sortField === "reference") {
        aVal = String(a.referenceNumber);
        bVal = String(b.referenceNumber);
      }
      if (aVal < bVal) return sortOrder === "asc" ? -1 : 1;
      if (aVal > bVal) return sortOrder === "asc" ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [filteredOrders, sortField, sortOrder]);

  // Calculate pagination details
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage) || 1;
  const paginatedOrders = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedOrders.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedOrders, currentPage, itemsPerPage]);

  // Handle sorting logic when a header is clicked
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
  };

  // Ensure the current page remains valid when navigating pages
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Reset page when search term changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  return (
    <div className="w-full">
      {/* Combined Search & Items per Page Controls */}
      <div className="mb-4 flex flex-col space-y-4">
        <div className="flex justify-between">
          <Input
            className="mr-4 w-full"
            placeholder="Search by project, vendor, or reference..."
            value={searchTerm}
            onChange={handleSearchChange}
          />
          <Select
            value={itemsPerPage.toString()}
            onValueChange={(value) => {
              setItemsPerPage(Number(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue placeholder="Items" />
            </SelectTrigger>
            <SelectContent>
              {[5, 10, 20, 50].map((num) => (
                <SelectItem key={num} value={num.toString()}>
                  {num}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>#</TableHead>
              <TableHead
                onClick={() => handleSort("project")}
                className="cursor-pointer"
              >
                Project{" "}
                {sortField === "project" && (sortOrder === "asc" ? "↑" : "↓")}
              </TableHead>
              <TableHead
                onClick={() => handleSort("vendor")}
                className="cursor-pointer"
              >
                Company{" "}
                {sortField === "vendor" && (sortOrder === "asc" ? "↑" : "↓")}
              </TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Status</TableHead>

              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOrders.map((po) => {
              if (!po.PurchaseOrderDetails) return null;
              return (
                <TableRow key={po.purchaseOrderId}>
                  <TableCell>{po.referenceNumber}</TableCell>
                  <TableCell>
                    {po.PurchaseOrderDetails.project.projectName}
                  </TableCell>
                  <TableCell>
                    {po.PurchaseOrderDetails.company.companyName}
                  </TableCell>
                  <TableCell>
                    <HoverCard>
                      <HoverCardTrigger>
                        {po.PurchaseOrderDetails.description
                          ? po.PurchaseOrderDetails.description.slice(0, 10) +
                            "..."
                          : "N/A"}
                      </HoverCardTrigger>
                      <HoverCardContent className="w-80">
                        {po.PurchaseOrderDetails.description || ""}
                      </HoverCardContent>
                    </HoverCard>
                  </TableCell>
                  <TableCell>
                    {po.paymentType === "cash"
                      ? "Single Payment"
                      : po.paymentType}
                  </TableCell>
                  <TableCell>
                    {po.PurchaseOrderDetails.totalAmount.toLocaleString(
                      "en-US",
                      {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      },
                    )}{" "}
                    {po.PurchaseOrderDetails.currency}
                  </TableCell>
                  <TableCell>
                    {po.isDraft
                      ? "Draft"
                      : po.PurchaseOrderDetails.PurchaseOrderPayments.some(
                            (p) => p.status === "rejected",
                          )
                        ? "Rejected"
                        : po.isPaid
                          ? "Paid"
                          : po.PurchaseOrderDetails.PurchaseOrderPayments.some(
                                (p) =>
                                  p.status !== "idle" && p.status !== "paid",
                              )
                            ? "In progress"
                            : "In progress"}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/purchaseOrder/process/${po.purchaseOrderId}`,
                            )
                          }
                        >
                          View Purchase Order
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(`/purchaseOrder/${po.purchaseOrderId}`)
                          }
                        >
                          Edit Purchase Order
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          {paginatedOrders.length === 0 && (
            <TableFooter>
              <TableRow>
                <TableCell colSpan={9} className="text-center">
                  No purchase orders found.
                  {purchaseOrders.length === 0 && (
                    <div className="mt-2">
                      <Link href="/purchaseOrder/new">
                        <Button>Create your first PO</Button>
                      </Link>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          )}
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="mt-4 flex items-center justify-between">
        <div>
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
