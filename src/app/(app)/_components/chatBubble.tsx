"use client";

import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { MessageSquare, X, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { api } from "@/trpc/react";
import { useSession } from "next-auth/react";
import { Company } from "@prisma/client";

export default function ChatBubble({
  purchaseOrderId,
  defualtOpen,
}: {
  purchaseOrderId: string;
  defualtOpen?: boolean;
}) {
  const { data } = useSession();
  const {
    data: threads,
    isLoading,
    refetch: refetchPO,
  } = api.thread.getPoThread.useQuery({ purchaseOrderId });
  const { mutate } = api.thread.sendMessage.useMutation({
    onSuccess: () => {
      refetchPO();
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");

  // We'll store the chat container element in state.
  const [containerEl, setContainerEl] = useState<HTMLDivElement | null>(null);

  // Callback ref: update state and immediately scroll the container when it mounts.
  const setViewRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      node.scrollTop = node.scrollHeight;
    }
    setContainerEl(node);
  }, []);

  const toggleChat = () => {
    refetchPO();
    setIsOpen((prev) => !prev);
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      mutate({
        purchaseOrderId,
        message: message.trim(),
      });
      setMessage("");
    }
  };

  // Open chat by default if requested.
  useEffect(() => {
    if (defualtOpen) {
      setIsOpen(true);
    }
  }, [defualtOpen]);

  // When threads update, scroll to the bottom.
  useEffect(() => {
    if (containerEl) {
      containerEl.scrollTop = containerEl.scrollHeight;
    }
  }, [threads, containerEl]);

  return (
    <div className="fixed bottom-6 right-6 z-50 print:hidden">
      <AnimatePresence mode="wait">
        {isOpen ? (
          <motion.div
            key="chat-box"
            initial={{ scale: 0.8, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 20 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="origin-bottom-right"
          >
            <Card className="flex h-96 w-80 flex-col shadow-lg sm:w-96">
              <div
                onClick={toggleChat}
                className="flex items-center justify-between border-b bg-primary p-3 text-primary-foreground"
              >
                <h3 className="font-medium">Thread</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 cursor-pointer text-primary-foreground"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Use the callback ref */}
              <div
                ref={setViewRef}
                className="flex-1 space-y-3 overflow-y-auto p-3"
              >
                {(threads || []).map((msg, index) => {
                  const isUser = msg.user.id === data?.user.id;
                  if (msg.type === "message") {
                    return (
                      <div
                        key={index}
                        className={`flex ${isUser ? "justify-end" : "justify-start"}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-3 ${
                            isUser
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          }`}
                        >
                          <div className="text-xs font-semibold text-gray-600">
                            {msg.user.username},{" "}
                            {msg.createdAt.toLocaleString()}
                          </div>
                          <div>{msg.message}</div>
                        </div>
                      </div>
                    );
                  }
                  return (
                    <div
                      key={index}
                      className="flex flex-col items-center justify-center border-b border-dashed pb-2"
                    >
                      <small className="max-w-[80%] rounded-lg text-center text-xs text-gray-400">
                        {msg.message}
                      </small>
                      <small className="max-w-[80%] rounded-lg text-center text-xs text-gray-400">
                        <span className="font-bold">{msg.user.username}</span>,{" "}
                        {msg.createdAt.toLocaleString()}
                      </small>
                    </div>
                  );
                })}
              </div>

              <form
                onSubmit={handleSendMessage}
                className="flex gap-2 border-t p-3"
              >
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1"
                />
                <Button type="submit" size="icon">
                  <Send className="h-4 w-4" />
                </Button>
              </form>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            key="chat-bubble"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="cursor-pointer rounded-full bg-primary p-3 text-primary-foreground shadow-lg"
            onClick={toggleChat}
          >
            <MessageSquare className="h-6 w-6" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
