"use client";

import { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { UploadButton } from "@/components/uploadThings";
import { ClientUploadedFileData } from "uploadthing/types";
import { toast } from "react-toastify";
import { useChannel } from "ably/react";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
// Custom Components
import SelectCreate from "../../_components/select-create";
import SimpleCard from "../_components/simple-card";
import CardLoader from "@/app/_components/cardLoader";
import ChatBubble from "../../_components/chatBubble";
import ImageViewer from "@/components/ui/ImageViewer";

// Icons
import {
  BanknoteIcon,
  CalendarIcon,
  CheckIcon,
  ChevronsUpDownIcon,
  ClipboardListIcon,
  CreditCardIcon,
  DollarSignIcon,
  EyeIcon,
  FileTextIcon,
  History,
  Info,
  InfoIcon,
  Loader2,
  Loader2Icon,
  PlusIcon,
  SaveIcon,
  SendIcon,
  SettingsIcon,
  TrashIcon,
  UserIcon,
  X,
  XIcon,
} from "lucide-react";

// API
import { api } from "@/trpc/react";

// Types
import {
  Currency,
  PaymentMethod,
  PurchaseOrder,
  PurchaseOrderAttachment,
  PurchaseOrderDetails,
  PurchaseOrderPayment,
  PurchaseOrderItem,
  User,
  Attachment,
} from "@prisma/client";
import { AttachmentUploader } from "@/app/_components/attachmentUploader";
import Link from "next/link";
import { statusNameConverter } from "@/lib/utils";

const newItem = {
  date: new Date(),
  description: "",
  note: "",
  paid: false,
  priceNoTax: 0,
  priceTax: 0,
  purchaseOrderDetailId: "",
  purchaseOrderItemId: "",
  taxAmount: 0,
};

export default function PurchaseOrderPage() {
  const { purchaseOrderId } = useParams<{ purchaseOrderId: string }>();
  const router = useRouter();
  const { channel } = useChannel("notifications");
  const isNewOrder = purchaseOrderId === "new";
  const { data: users } = api.user.getAll.useQuery();

  // State
  const [attachments, setAttachments] = useState<
    ClientUploadedFileData<{ uploadedBy: string }>[]
  >([]);

  const [po, setPo] = useState<
    Partial<
      PurchaseOrder &
        PurchaseOrderDetails & {
          purchaseOrderAttachments: (PurchaseOrderAttachment & {
            Attachment: Attachment;
          })[];
        }
    >
  >({
    paymentType: "cash",
    currency: "JOD",
    paymentMethod: "bankTransfer",
    isDraft: false,
    isPaid: false,
    totalAmount: 0,
  });

  const [toUpdate, setToUpdate] = useState<boolean>(false);
  const [open, setOpen] = useState(false);
  const [navigationModal, setNavigationModal] = useState(false);

  const [items, setItems] = useState<
    Partial<PurchaseOrderItem & { changed?: boolean }>[]
  >([
    {
      taxAmount: 0,
      priceNoTax: 0,
      priceTax: 0,
      description: "Item 1",
    },
  ]);

  const [payments, setPayments] = useState<
    Partial<PurchaseOrderPayment & { changed?: boolean }>[]
  >([
    {
      amount: 0,
      date: new Date(),
      description: "",
      percentage: 0,
      status: "idle",
    },
  ]);
  const [paymentsToReview, setPaymentsToReview] = useState<
    Partial<PurchaseOrderPayment>[]
  >([]);

  // Queries
  const {
    data: companies,
    refetch: refetchCompanies,
    isLoading: loadingCompanies,
  } = api.company.getAll.useQuery();

  const {
    data: projects,
    refetch: refetchProjects,
    isLoading: loadingProjects,
  } = api.project.getAll.useQuery();

  const {
    data: purchaseOrder,
    status: poStatus,
    isLoading: loadingPurchaseOrder,
    refetch: refetchPO,
  } = api.purchaseOrder.getAllPurchaseOrderData.useQuery({
    purchaseOrderId,
  });

  // helpers

  // Mutations
  // FIXME
  const { mutate: deletePo, isPending: loadingDelete } =
    api.purchaseOrder.delete.useMutation({
      onSuccess: () => {
        toast.success("Purchase Order Deleted Successfully");
        setOpen(false);
        router.push("/purchaseOrder/new");
      },
    });
  const { mutate: sendToReview, isPending: loadingReview } =
    api.purchaseOrder.sendToReview.useMutation({
      onSuccess: (res) => {
        setShowApproveModal(false);
        if (!purchaseOrder) return;
        res.notifications.forEach((notification) => {
          console.log(notification);
          const p = payments.find(
            (payment) =>
              payment.PurchaseOrderPaymentId ===
              notification.PurchaseOrderPaymentId,
          );
          if (p) {
            toast.success(`Payment ${p.description} sent for review`);
          }
        });
        // TODO
        setNavigationModal(true);
        refetchPO();
        const { notifications } = res;
        notifications.forEach((notification) => {
          console.log(notification);
          channel.publish(
            "po-notifi",
            JSON.stringify({
              ...notification,
            }),
          );
        });
        router.refresh();
      },
    });

  useEffect(() => {
    if (purchaseOrder && poStatus === "success" && !loadingPurchaseOrder) {
      if (
        purchaseOrder?.PurchaseOrderDetails &&
        purchaseOrder.paymentType === "cash" &&
        (purchaseOrder.PurchaseOrderDetails?.PurchaseOrderPayments[0]
          ?.status === "idle" ||
          purchaseOrder.isDraft)
      ) {
        sendToReview({
          payments: purchaseOrder.PurchaseOrderDetails
            ?.PurchaseOrderPayments as any,
          purchaseOrderId: purchaseOrder.purchaseOrderId,
          companyName: purchaseOrder?.PurchaseOrderDetails.company.companyName,
          projectName: purchaseOrder?.PurchaseOrderDetails.project.projectName,
          userReviewId: purchaseOrder.userReviewId,
          paymentType: purchaseOrder.paymentType,
        });
      }
    }
  }, [purchaseOrder]);
  const { mutate: createAttachment } = api.attachment.create.useMutation();
  const { mutate: deleteAttachment } = api.attachment.delete.useMutation();
  const { mutate: deleteAttachmentFromUT } =
    api.attachment.deleteFromUT.useMutation();

  const createPo = api.purchaseOrder.create.useMutation({
    onSuccess: (po) => {
      setAttachments([]);
      if (isNewOrder) {
        toast.success("Purchase Order Created Successfully");
        setTimeout(() => {
          router.push(`/purchaseOrder/${po.purchaseOrderId}`);
        }, 2000);
      } else {
        refetchPO();
        toast.success("Purchase Order Updated Successfully");
      }
    },
    onError: (error) => {
      console.log(error);

      toast.error(`Error: ${error.message}`);
    },
  });

  const updatePo = api.purchaseOrder.update.useMutation({
    onSuccess: (po) => {
      setAttachments([]);
      if (
        payments
          .filter((p) => p.status !== "paid")
          .some((p) => p.status !== "idle")
      ) {
        toast(
          purchaseOrder?.paymentType === "cash"
            ? "Purchase Order sent to review"
            : "Payments sent to review.",
          { delay: 700 },
        );
      }

      toast.success("Purchase Order Updated Successfully");
      if (!purchaseOrder) return;
      const { notifications } = po;
      notifications.forEach((notification) => {
        channel.publish(
          "po-notifi",
          JSON.stringify({
            ...notification,
          }),
        );
      });
      refetchPO();
    },
    onError: (error) => {
      console.log(error);

      toast.error(`Error: ${error.message}`);
    },
  });

  const {
    mutate: deleteItem,
    isPending: deleteItemIsPending,
    variables: deleteItemVariables,
  } = api.purchaseOrder.deleteItem.useMutation({
    onSuccess: (item) => {
      setItems(
        items.filter((i) => i.purchaseOrderItemId !== item.purchaseOrderItemId),
      );
      toast.success("Item deleted successfully");
    },
  });

  const {
    mutate: deletePayment,
    isPending: deletePaymentIsPending,
    variables: deletePaymentVariables,
  } = api.purchaseOrder.deletePayment.useMutation({
    onSuccess: (item) => {
      setPayments(
        payments.filter(
          (i) => i.PurchaseOrderPaymentId !== item.PurchaseOrderPaymentId,
        ),
      );
      toast.success("Payment deleted successfully");
    },
  });

  // Calculations
  const taxAmount = useMemo(() => {
    return items.reduce((acc, item) => {
      if (item) {
        const taxDifference = (item.priceTax || 0) - (item.priceNoTax || 0);
        // Only add positive tax differences
        if (taxDifference > 0) {
          acc += taxDifference;
        }
      }
      return acc;
    }, 0);
  }, [items]);

  const whtAmount = useMemo(() => {
    return items.reduce((acc, item) => {
      if (item) {
        const taxDifference = (item.priceTax || 0) - (item.priceNoTax || 0);
        // Only add negative tax differences (as positive values)
        if (taxDifference < 0) {
          acc += Math.abs(taxDifference);
        }
      }
      return acc;
    }, 0);
  }, [items]);

  const totalAmount = useMemo(() => {
    const total = items.reduce((acc, item) => {
      if (item) {
        acc += item.priceTax || 0;
      }
      return acc;
    }, 0);

    // Update payment percentages
    if (purchaseOrder?.paymentType === "installment") {
      setPayments((prevPayments) =>
        prevPayments.map((payment) => ({
          ...payment,
          percentage: total
            ? +(((payment.amount || 0) / total) * 100).toFixed(2)
            : 0,
        })),
      );
    } else {
      setPayments([
        {
          ...payments[0],
          percentage: 100,
          amount: total,
          changed: true,
        },
      ]);
    }

    return total;
  }, [items]);

  const installmentError = useMemo(() => {
    if (po.paymentType !== "installment") return false;

    const totalPayments = payments.reduce((acc, payment) => {
      return acc + (payment.amount || 0);
    }, 0);

    return Math.abs(totalPayments - totalAmount) > 0.01;
  }, [payments, totalAmount, po.paymentType]);

  const saveActive = useMemo(() => {
    if (!po.companyId || !po.projectId) return false;

    if (po.paymentType === "cash") {
      return !!(totalAmount && po.paymentMethod && po.userReviewId);
    } else {
      return !installmentError;
    }
  }, [po, totalAmount, installmentError]);

  const updateItem = (
    index: number,
    newValue: Partial<PurchaseOrderItem & { changed?: boolean }>,
  ) => {
    setItems((prevItems) =>
      prevItems.map((item, i) =>
        i === index ? { ...item, ...newValue } : item,
      ),
    );
  };
  const deleteAttachmentUTHandler = (keys: string[]) => {
    deleteAttachmentFromUT(
      {
        keys: keys,
      },
      {
        onSuccess: () => {
          setAttachments(
            attachments.filter((attachment) => !keys.includes(attachment.key)),
          );
        },
      },
    );
  };
  const deleteAttachmentHandler = ({
    attachmentId,
    key,
    purchaseOrderAttachmentId,
  }: {
    attachmentId: string;
    key: string;
    purchaseOrderAttachmentId: string;
  }) => {
    if (!purchaseOrder) return;
    deleteAttachment(
      {
        attachmentId: attachmentId,
        key: key,
        purchaseOrderAttachmentId: purchaseOrderAttachmentId,
      },
      {
        onSuccess: () => {
          refetchPO();
          toast.success("Attachment deleted successfully");
        },
      },
    );
  };

  const updatePayment = (
    index: number,
    newValue: Partial<PurchaseOrderPayment & { changed?: boolean }>,
  ) => {
    setPayments((prevPayments) =>
      prevPayments.map((payment, i) =>
        i === index ? { ...payment, ...newValue } : payment,
      ),
    );
  };

  const handleCreatePo = async () => {
    const payload = {
      po,
      totalAmount,
      items,
      payments,
      attachments,
      // installments: payments,
    };
    console.log(payload);
    // return;
    createPo.mutate(payload as any);
  };

  const handleUpdatePo = async () => {
    const updatedPayments = payments.filter((payment) => payment?.changed);
    const newPayments = payments.filter(
      (payment) => !payment.PurchaseOrderPaymentId,
    );
    console.log(updatedPayments);
    console.log(newPayments);

    const updatedItems = items.filter((item) => item?.changed);
    const newItems = items.filter((item) => !item.purchaseOrderItemId);

    const payload = {
      updatedPayments,
      newPayments,
      updatedItems,
      newItems,
      totalAmount,
      po,
    };
    console.log(payload);

    updatePo.mutate(payload as any);
  };

  const createPoAttachments = async (
    purchaseOrderId: string,
    attachments: ClientUploadedFileData<{ uploadedBy: string }>[],
  ) => {
    createAttachment(
      {
        purchaseOrderId,
        attachments,
      } as any,
      {
        onSuccess: (newAttachments) => {
          setPo({
            ...po,
            purchaseOrderAttachments: [...(newAttachments as any)],
          });
          setAttachments([]);
          toast.success("Attachment added successfully");
        },
      },
    );
  };

  // Effects
  useEffect(() => {
    if (po.paymentType === "cash" && !purchaseOrder) {
      setPayments([
        {
          amount: totalAmount,
          date: new Date(),
          description: "Full Payment",
          percentage: 100,
          status: "idle",
        },
      ]);
    }
  }, [po.paymentType, totalAmount]);
  useEffect(() => {
    if (users?.length) {
      setPo({
        ...po,
        userApproveId: users?.find((u) => u.role.role === "ceo")?.id || "",
        userAuditId: users?.find((u) => u.role.role === "auditor")?.id || "",
      });
    }
  }, [users]);

  useEffect(() => {
    if (!loadingPurchaseOrder && purchaseOrder) {
      setPo({ ...purchaseOrder, ...purchaseOrder.PurchaseOrderDetails });
      setItems(purchaseOrder.PurchaseOrderDetails?.PurchaseOrderItems as any);
      setPayments([
        ...(purchaseOrder.PurchaseOrderDetails?.PurchaseOrderPayments as any),
      ]);
      setToUpdate(false);

      if (
        purchaseOrder.paymentType === "cash" &&
        purchaseOrder.PurchaseOrderDetails?.PurchaseOrderPayments.length !== 1
      ) {
        toast.error("Please Contact Support!");
      }
      if (purchaseOrder.deletedAt) {
        toast.error("This Purchase Order is deleted");
        router.push("/purchaseOrder");
      }
      // setAttachments((purchaseOrder?.purchaseOrderAttachments as any) || []);
    }
    console.log(purchaseOrder);
  }, [purchaseOrder]);
  useEffect(() => {
    if (purchaseOrder && attachments?.length) {
      createPoAttachments(purchaseOrder.purchaseOrderId, attachments);
    }
  }, [attachments]);

  useEffect(() => {
    console.log(payments);
  }, [payments]);

  const ammountError = useMemo(() => {
    if (po.paymentType === "cash") return false;
    const totalPaidAmount = payments.reduce((acc, payment) => {
      if (payment.status === "paid") return acc + (payment?.amount || 0);
      else return acc;
    }, 0);
    return totalPaidAmount > totalAmount;
  }, [totalAmount, payments]);

  const [showApproveModal, setShowApproveModal] = useState(false);
  const handleSendForApproval = () => {
    if (!purchaseOrder) return;
    const toSend =
      purchaseOrder.paymentType === "cash" ? payments : paymentsToReview;

    if (
      !toSend?.length ||
      toSend.some((p) => p.status === "paid" || !p.PurchaseOrderPaymentId)
    ) {
      toast.error("No payments to review!!");
      return;
    }
    setShowApproveModal(false);
    sendToReview({
      payments: toSend as any,
      purchaseOrderId: purchaseOrder.purchaseOrderId,
      companyName:
        companies?.find((c) => c.companyId === po.companyId)?.companyName || "",
      projectName:
        projects?.find((p) => p.projectId === po.projectId)?.projectName || "",
      userReviewId: purchaseOrder.userReviewId,
      paymentType: purchaseOrder.paymentType,
    });
  };

  if (loadingCompanies || loadingProjects || loadingPurchaseOrder) {
    return <CardLoader message="Preparing purchase order, just a moment..." />;
  }

  console.log(payments);

  return (
    <div className="container mx-auto p-4">
      {purchaseOrder && (
        <ChatBubble
          defualtOpen={payments.some((p) => p.status === "rejected")}
          purchaseOrderId={purchaseOrderId}
        />
      )}

      <Card className="overflow-hidden border-none shadow-lg">
        <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold">
              {!purchaseOrder
                ? "Create Purchase Order"
                : "Update Purchase Order"}{" "}
              {purchaseOrder &&
              purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.every(
                (p) => p.status !== "paid",
              ) &&
              !purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.every(
                (p) => p.status === "idle",
              ) &&
              !purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.some(
                (p) => p.status === "rejected",
              ) ? (
                purchaseOrder?.paymentType === "cash" ? (
                  <span className="stamp">
                    {statusNameConverter(payments[0]?.status || "").name}
                  </span>
                ) : (
                  <span className="stamp">In progress</span>
                )
              ) : null}
              {purchaseOrder &&
              purchaseOrder.PurchaseOrderDetails?.PurchaseOrderPayments.some(
                (p) => p.status === "rejected",
              ) ? (
                <span className="stamp is-nope">Rejected</span>
              ) : null}
              {purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.every(
                (p) => p.status === "paid",
              ) && purchaseOrder?.isPaid ? (
                <span className="stamp is-approved">Paid</span>
              ) : null}
              {purchaseOrder &&
              purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.every(
                (p) => p.status === "idle",
              ) ? (
                <span className="stamp is-draft">D R A F T</span>
              ) : null}
            </CardTitle>

            {purchaseOrder?.isPaid ? (
              <Button
                variant={"outline"}
                onClick={() => {
                  router.push(
                    `/purchaseOrder/process/${purchaseOrder.purchaseOrderId}`,
                  );
                }}
              >
                View <EyeIcon />
              </Button>
            ) : (
              <div className="flex gap-2">
                {purchaseOrder ? (
                  <>
                    {toUpdate || ammountError ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              onClick={handleUpdatePo}
                              className={
                                !saveActive || ammountError ? "bg-red-300" : ""
                              }
                              disabled={
                                !saveActive ||
                                updatePo.isPending ||
                                ammountError
                              }
                            >
                              {createPo.isPending ? (
                                <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                              ) : (
                                <SaveIcon className="mr-2 h-4 w-4" />
                              )}
                              update
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Save your progress</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : payments.some((p) => p.status === "rejected") ? (
                      <Button variant={"destructive"} disabled>
                        Purchase Order Rejected
                      </Button>
                    ) : (
                      <Dialog
                        open={showApproveModal}
                        onOpenChange={setShowApproveModal}
                      >
                        <DialogTrigger
                          disabled={
                            !saveActive ||
                            toUpdate ||
                            createPo.isPending ||
                            payments.every((p) => p.status !== "idle") ||
                            payments.some((p) => p.status === "rejected")
                          }
                          asChild
                        >
                          <Button
                            disabled={
                              !saveActive ||
                              toUpdate ||
                              createPo.isPending ||
                              payments.every((p) => p.status !== "idle") ||
                              payments.some((p) => p.status === "rejected")
                            }
                            onClick={() => setShowApproveModal(true)}
                          >
                            {createPo.isPending ? (
                              <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <SendIcon className="mr-2 h-4 w-4" />
                            )}
                            Send for approval
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl">
                          <DialogHeader>
                            <DialogTitle>Send for approval</DialogTitle>
                            <DialogDescription>
                              {purchaseOrder.paymentType === "installment"
                                ? "Select the installments to be sent for approval"
                                : "Are you sure you want to send this purchase order for approval?"}
                            </DialogDescription>
                          </DialogHeader>
                          {purchaseOrder.paymentType === "installment" ? (
                            <div>
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Percentage</TableHead>
                                    <TableHead>Due Date</TableHead>
                                    <TableHead>Select</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {/* sorted from oldest to latest on the {date} */}
                                  {payments
                                    .sort(
                                      (a, b) =>
                                        (a?.date || new Date()).getTime() -
                                        (b?.date || new Date()).getTime(),
                                    )
                                    .map((payment, i) => {
                                      return (
                                        <TableRow key={i}>
                                          <TableCell className="text-nowrap">
                                            {payment.description}
                                          </TableCell>
                                          <TableCell className="text-nowrap">
                                            {(
                                              payment?.amount || 0
                                            )?.toLocaleString("en-US", {
                                              minimumFractionDigits: 2,
                                              maximumFractionDigits: 2,
                                            })}{" "}
                                            {
                                              purchaseOrder.PurchaseOrderDetails
                                                ?.currency
                                            }
                                          </TableCell>
                                          <TableCell>
                                            {payment.percentage}%
                                          </TableCell>
                                          <TableCell className="font-medium">
                                            {payment.date?.toLocaleDateString()}
                                          </TableCell>
                                          <TableCell>
                                            {payment.status === "idle" ? (
                                              <Switch
                                                checked={
                                                  !!paymentsToReview.find(
                                                    (p) =>
                                                      p.PurchaseOrderPaymentId ===
                                                      payment.PurchaseOrderPaymentId,
                                                  )
                                                }
                                                onCheckedChange={(checked) => {
                                                  if (checked) {
                                                    setPaymentsToReview([
                                                      ...paymentsToReview,
                                                      payment,
                                                    ]);
                                                  } else {
                                                    setPaymentsToReview([
                                                      ...paymentsToReview.filter(
                                                        (p) =>
                                                          p.PurchaseOrderPaymentId !==
                                                          payment.PurchaseOrderPaymentId,
                                                      ),
                                                    ]);
                                                  }
                                                }}
                                              />
                                            ) : (
                                              payment.status
                                            )}
                                          </TableCell>
                                        </TableRow>
                                      );
                                    })}
                                </TableBody>
                              </Table>
                            </div>
                          ) : (
                            <div></div>
                          )}
                          <DialogFooter>
                            <Button
                              disabled={
                                loadingReview ||
                                (!paymentsToReview.length &&
                                  purchaseOrder.paymentType === "installment")
                              }
                              onClick={handleSendForApproval}
                            >
                              {loadingReview ? (
                                <Loader2 className="animate-spin" />
                              ) : purchaseOrder.paymentType ===
                                "installment" ? (
                                <div className="group flex items-center gap-2">
                                  Send Selected To Review
                                  <SendIcon className="mr-2 h-4 w-4 transition-all delay-100 group-hover:-translate-y-1 group-hover:translate-x-1" />
                                </div>
                              ) : (
                                <div className="group flex items-center gap-2">
                                  Send To Review
                                  <SendIcon className="mr-2 h-4 w-4 transition-all delay-100 group-hover:-translate-y-1 group-hover:translate-x-1" />
                                </div>
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    )}
                  </>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={handleCreatePo}
                          disabled={!saveActive || createPo.isPending}
                        >
                          {createPo.isPending ? (
                            <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <SaveIcon className="mr-2 h-4 w-4" />
                          )}
                          Create Order
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Create a new order</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <SettingsIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                    <DropdownMenuLabel>Options</DropdownMenuLabel>
                    <DropdownMenuGroup>
                      {!isNewOrder && (
                        <AlertDialog open={open} onOpenChange={setOpen}>
                          <DropdownMenuItem
                            onSelect={(e) => {
                              e.preventDefault();
                              if (purchaseOrder?.purchaseOrderId) {
                                setOpen(true);
                              }
                            }}
                            disabled={!purchaseOrder?.purchaseOrderId}
                            className="cursor-pointer text-destructive focus:bg-destructive/10 focus:text-destructive"
                          >
                            <TrashIcon className="mr-2 h-4 w-4" />
                            Delete Purchase Order
                          </DropdownMenuItem>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Are you absolutely sure?
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will
                                permanently delete your Purchase Order.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => {
                                  deletePo({
                                    purchaseOrderId:
                                      purchaseOrder?.purchaseOrderId || "",
                                  });
                                }}
                              >
                                {loadingDelete ? (
                                  <Loader2 className="animate-spin" />
                                ) : (
                                  "Delete"
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="mb-6 grid w-full grid-cols-4">
              <TabsTrigger value="details">
                <FileTextIcon className="mr-2 h-4 w-4" />
                Basic Details
              </TabsTrigger>
              <TabsTrigger value="items">
                <ClipboardListIcon className="mr-2 h-4 w-4" />
                Items
              </TabsTrigger>
              <TabsTrigger value="payments">
                <CreditCardIcon className="mr-2 h-4 w-4" />
                Payments
              </TabsTrigger>
              <TabsTrigger value="approvals">
                <CheckIcon className="mr-2 h-4 w-4" />
                Approvals
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <SimpleCard title="Project & Vendor Information">
                  <div className="space-y-4">
                    <SelectCreate
                      disabled={purchaseOrder?.isPaid}
                      label="Project"
                      placeholder="Select Project"
                      noOptionsMessage={({ inputValue }) =>
                        `Ask the accountant to create "${inputValue}"`
                      }
                      value={{
                        value: po.projectId || "",
                        label:
                          projects?.find((p) => p.projectId === po.projectId)
                            ?.projectName || "",
                      }}
                      onChange={(selected) => {
                        if (purchaseOrder) {
                          setToUpdate(true);
                        }
                        setPo({
                          ...po,
                          projectId: selected?.value || "",
                        });
                      }}
                      options={
                        projects?.map((project) => ({
                          value: project.projectId,
                          label: project.projectName,
                        })) || []
                      }
                    />

                    <SelectCreate
                      disabled={purchaseOrder?.isPaid}
                      label="Vendor"
                      extraComponent={
                        po.companyId ? (
                          <Link
                            target="_blank"
                            href={`/view/vendor/${po.companyId}`}
                          >
                            <HoverCard>
                              <HoverCardContent>
                                View vendor profile
                              </HoverCardContent>
                              <HoverCardTrigger>
                                <Info size={15} />
                              </HoverCardTrigger>
                            </HoverCard>
                          </Link>
                        ) : null
                      }
                      placeholder="Select Company"
                      noOptionsMessage={({ inputValue }) =>
                        `Ask the accountant to create "${inputValue}"`
                      }
                      value={{
                        value: po.companyId || "",
                        label:
                          companies?.find((c) => c.companyId === po.companyId)
                            ?.companyName || "",
                      }}
                      onChange={(selected) => {
                        const company = companies?.find(
                          (co) => co.companyId === selected.value,
                        );

                        const companyDetails = {
                          paymentMethod: company?.paymentMethod,
                          contactName: company?.contactPerson,
                          contactNumber: company?.contactPhone,
                        } as any;

                        if (companyDetails?.paymentMethod === "bankTransfer") {
                          companyDetails.iban = company?.iban || "";
                          companyDetails.swiftCode = company?.swiftCode || "";
                          companyDetails.accountName =
                            company?.accountName || "";
                        } else if (
                          companyDetails.paymentMethod === "eFAWATEERCOM"
                        ) {
                          companyDetails.identifier = company?.identifier || "";
                        } else if (companyDetails.paymentMethod === "cheque") {
                          companyDetails.nameOnCheque =
                            company?.nameOnCheque || "";
                        } else if (companyDetails.paymentMethod === "cliq") {
                          companyDetails.cliq = company?.cliq || "";
                        }
                        if (purchaseOrder) {
                          setToUpdate(true);
                        }
                        setPo({
                          ...po,
                          companyId: selected?.value || "",
                          ...(companyDetails?.paymentMethod
                            ? companyDetails
                            : {}),
                        });
                      }}
                      options={
                        companies?.map((company) => ({
                          value: company.companyId,
                          label: company.companyName,
                        })) || []
                      }
                    />

                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="contactName">Contact Name</Label>
                        <Input
                          disabled={purchaseOrder?.isPaid}
                          id="contactName"
                          placeholder="Contact person name"
                          value={po.contactName || ""}
                          onChange={(e) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({ ...po, contactName: e.target.value });
                          }}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="contactNumber">Contact Number</Label>
                        <Input
                          disabled={purchaseOrder?.isPaid}
                          id="contactNumber"
                          placeholder="Contact phone number"
                          value={po.contactNumber || ""}
                          onChange={(e) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            const isValueNumber = /^\d+$/.test(e.target.value);
                            if (isValueNumber || e.target.value === "")
                              setPo({ ...po, contactNumber: e.target.value });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </SimpleCard>

                <SimpleCard title="Payment Details">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          disabled={purchaseOrder?.isPaid}
                          value={po.currency || undefined}
                          onValueChange={(value: Currency) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({ ...po, currency: value });
                          }}
                        >
                          <SelectTrigger id="currency">
                            <SelectValue placeholder="Select Currency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Currency</SelectLabel>
                              <SelectItem value="JOD">
                                JOD - Jordanian Dinar
                              </SelectItem>
                              <SelectItem value="USD">
                                USD - US Dollar
                              </SelectItem>
                              <SelectItem value="EUR">EUR - Euro</SelectItem>
                              <SelectItem value="GBP">
                                GBP - British Pound
                              </SelectItem>
                              <SelectItem value="AED">
                                AED - UAE Dirham
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="paymentMethod">Payment Method</Label>
                        <Select
                          disabled={purchaseOrder?.isPaid}
                          value={po.paymentMethod || undefined}
                          onValueChange={(value: PaymentMethod) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({ ...po, paymentMethod: value });
                          }}
                        >
                          <SelectTrigger id="paymentMethod">
                            <SelectValue placeholder="Select Payment Method" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Payment Method</SelectLabel>
                              <SelectItem value="bankTransfer">
                                Bank Transfer
                              </SelectItem>
                              <SelectItem value="cash">Cash</SelectItem>
                              <SelectItem value="cheque">Cheque</SelectItem>
                              <SelectItem value="CLIQ">CLIQ</SelectItem>
                              <SelectItem value="eFAWATEERCOM">
                                eFAWATEERCOM
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {po.paymentMethod === "bankTransfer" && (
                      <div className="rounded-md border border-border bg-muted/20 p-4">
                        <h3 className="mb-3 font-medium">
                          Bank Transfer Details
                        </h3>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                          <div className="space-y-2">
                            <Label htmlFor="iban">IBAN</Label>
                            <Input
                              disabled={purchaseOrder?.isPaid}
                              id="iban"
                              placeholder="International Bank Account Number"
                              value={po.iban || ""}
                              onChange={(e) => {
                                if (purchaseOrder) {
                                  setToUpdate(true);
                                }
                                setPo({ ...po, iban: e.target.value });
                              }}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="swiftCode">Swift Code</Label>
                            <Input
                              disabled={purchaseOrder?.isPaid}
                              id="swiftCode"
                              placeholder="Bank Swift Code"
                              value={po.swiftCode || ""}
                              onChange={(e) => {
                                if (purchaseOrder) {
                                  setToUpdate(true);
                                }
                                setPo({ ...po, swiftCode: e.target.value });
                              }}
                            />
                          </div>

                          <div className="col-span-1 space-y-2 sm:col-span-2">
                            <Label htmlFor="accountName">Account Name</Label>
                            <Input
                              disabled={purchaseOrder?.isPaid}
                              id="accountName"
                              placeholder="Name on the bank account"
                              value={po.accountName || ""}
                              onChange={(e) => {
                                if (purchaseOrder) {
                                  setToUpdate(true);
                                }
                                setPo({ ...po, accountName: e.target.value });
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {po.paymentMethod === "CLIQ" && (
                      <div className="space-y-2">
                        <Label htmlFor="cliq">
                          CLIQ{" "}
                          <span className="text-xs text-muted-foreground">
                            (Alias / number)
                          </span>
                        </Label>
                        <Input
                          disabled={purchaseOrder?.isPaid}
                          id="cliq"
                          placeholder="CLIQ alias or number"
                          value={po.cliq || ""}
                          onChange={(e) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({
                              ...po,
                              cliq: e.target.value.toUpperCase(),
                            });
                          }}
                        />
                      </div>
                    )}

                    {po.paymentMethod === "cheque" && (
                      <div className="space-y-2">
                        <Label htmlFor="nameOnCheque">Name On Cheque</Label>
                        <Input
                          disabled={purchaseOrder?.isPaid}
                          id="nameOnCheque"
                          placeholder="Name to appear on cheque"
                          value={po.nameOnCheque || ""}
                          onChange={(e) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({ ...po, nameOnCheque: e.target.value });
                          }}
                        />
                      </div>
                    )}

                    {po.paymentMethod === "eFAWATEERCOM" && (
                      <div className="space-y-2">
                        <Label htmlFor="identifier">Identifier</Label>
                        <Input
                          disabled={purchaseOrder?.isPaid}
                          id="identifier"
                          placeholder="eFAWATEERCOM identifier"
                          value={po.identifier || ""}
                          onChange={(e) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            setPo({ ...po, identifier: e.target.value });
                          }}
                        />
                      </div>
                    )}
                  </div>
                </SimpleCard>

                <div className="col-span-1 sm:col-span-2">
                  <SimpleCard title="Additional Information">
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        disabled={purchaseOrder?.isPaid}
                        id="description"
                        placeholder="Add any additional notes or details about this purchase order"
                        className="min-h-[120px]"
                        value={po.description || ""}
                        onChange={(e) => {
                          if (purchaseOrder) {
                            setToUpdate(true);
                          }
                          setPo({ ...po, description: e.target.value });
                        }}
                      />
                    </div>
                  </SimpleCard>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="items" className="space-y-6">
              <SimpleCard
                title="Order Items"
                description="Add all items included in this purchase order"
              >
                <div className="mb-4 flex justify-end">
                  <Button
                    disabled={purchaseOrder?.isPaid}
                    onClick={() => {
                      if (purchaseOrder) {
                        setToUpdate(true);
                      }
                      setItems([
                        ...items,
                        {
                          ...newItem,
                          description: `Item ${items.length + 1}`,
                        },
                      ]);
                    }}
                    className="flex items-center gap-1"
                  >
                    <PlusIcon className="mr-1 h-4 w-4" />
                    Add Item
                  </Button>
                </div>

                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Tax %</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      {items.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="h-24 text-center">
                            No items added yet. Click "Add Item" to get started.
                          </TableCell>
                        </TableRow>
                      ) : (
                        items.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Input
                                disabled={purchaseOrder?.isPaid}
                                className="w-full"
                                placeholder="Item description"
                                value={item.description || ""}
                                onChange={(e) => {
                                  if (purchaseOrder) {
                                    setToUpdate(true);
                                  }
                                  updateItem(index, {
                                    changed: !!item.purchaseOrderItemId,
                                    description: e.target.value,
                                  });
                                }}
                              />
                            </TableCell>

                            <TableCell>
                              <Input
                                disabled={purchaseOrder?.isPaid}
                                type="number"
                                placeholder="0.00"
                                className="w-full"
                                value={item.priceNoTax || ""}
                                onChange={(e) => {
                                  if (purchaseOrder) {
                                    setToUpdate(true);
                                  }
                                  const value = parseFloat(e.target.value);
                                  if (isNaN(value) || value >= 0) {
                                    const newItem = { ...item };
                                    const taxAmount = newItem.taxAmount || 0;

                                    newItem.priceNoTax = isNaN(value)
                                      ? 0
                                      : parseFloat(value.toFixed(3));

                                    if (taxAmount === 5) {
                                      newItem.priceTax = isNaN(value)
                                        ? 0
                                        : parseFloat((value / 0.95).toFixed(3));
                                    } else {
                                      newItem.priceTax = isNaN(value)
                                        ? 0
                                        : parseFloat(
                                            (
                                              value * (taxAmount / 100) +
                                              value
                                            ).toFixed(3),
                                          );
                                    }

                                    updateItem(index, {
                                      ...newItem,
                                      changed: !!item.purchaseOrderItemId,
                                    });
                                  }
                                }}
                              />
                            </TableCell>

                            <TableCell>
                              <Select
                                disabled={purchaseOrder?.isPaid}
                                value={item.taxAmount?.toString() || "0"}
                                onValueChange={(value) => {
                                  if (purchaseOrder) {
                                    setToUpdate(true);
                                  }
                                  const newItem = { ...item };
                                  const taxAmount = parseInt(value);

                                  newItem.taxAmount = taxAmount;

                                  if (newItem.priceNoTax) {
                                    if (taxAmount < 0) {
                                      const positiveTaxAmount = taxAmount * -1;
                                      console.log(positiveTaxAmount);

                                      newItem.priceTax = parseFloat(
                                        (
                                          (newItem.priceNoTax *
                                            (positiveTaxAmount / 100) -
                                            newItem.priceNoTax) *
                                          -1
                                        )
                                          // newItem.priceNoTax / 1 -
                                          // positiveTaxAmount
                                          .toFixed(3),
                                      );
                                    } else {
                                      newItem.priceTax = parseFloat(
                                        (
                                          newItem.priceNoTax *
                                            (taxAmount / 100) +
                                          newItem.priceNoTax
                                        ).toFixed(3),
                                      );
                                    }
                                  }

                                  updateItem(index, {
                                    ...newItem,
                                    changed: !!item.purchaseOrderItemId,
                                  });
                                }}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Tax %" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="0">0%</SelectItem>
                                  <SelectItem value="-5">5% (WHT)</SelectItem>
                                  <SelectItem value="5">5% (VAT)</SelectItem>
                                  <SelectItem value="7">7%</SelectItem>
                                  <SelectItem value="10">10%</SelectItem>
                                  <SelectItem value="-10">10% (WHT)</SelectItem>
                                  <SelectItem value="16">16%</SelectItem>
                                  <SelectItem value="26">26%</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>

                            <TableCell>
                              <Input
                                disabled={purchaseOrder?.isPaid}
                                type="number"
                                placeholder="0.00"
                                className="w-full"
                                value={item.priceTax || ""}
                                onChange={(e) => {
                                  if (purchaseOrder) {
                                    setToUpdate(true);
                                  }
                                  const value = parseFloat(e.target.value);
                                  if (isNaN(value) || value >= 0) {
                                    const newItem = { ...item };
                                    const taxAmount = newItem.taxAmount || 0;

                                    newItem.priceTax = isNaN(value)
                                      ? 0
                                      : parseFloat(value.toFixed(3));

                                    if (taxAmount === 5) {
                                      newItem.priceNoTax = isNaN(value)
                                        ? 0
                                        : parseFloat((value * 0.95).toFixed(3));
                                    } else {
                                      newItem.priceNoTax = isNaN(value)
                                        ? 0
                                        : parseFloat(
                                            (
                                              value /
                                              (1 + taxAmount / 100)
                                            ).toFixed(3),
                                          );
                                    }

                                    updateItem(index, {
                                      ...newItem,
                                      changed: !!item.purchaseOrderItemId,
                                    });
                                  }
                                }}
                              />
                            </TableCell>

                            <TableCell>
                              <Button
                                variant="destructive"
                                size="sm"
                                disabled={
                                  purchaseOrder?.isPaid ||
                                  (deleteItemIsPending &&
                                    deleteItemVariables?.purchaseOrderItemId ===
                                      item.purchaseOrderItemId)
                                }
                                onClick={() => {
                                  if (purchaseOrder) {
                                    setToUpdate(true);
                                  }
                                  if (item.purchaseOrderItemId) {
                                    deleteItem({
                                      purchaseOrderItemId:
                                        item.purchaseOrderItemId,
                                    });
                                  } else {
                                    setItems(
                                      items.filter((_, i) => i !== index),
                                    );
                                  }
                                }}
                              >
                                {deleteItemIsPending &&
                                deleteItemVariables?.purchaseOrderItemId ===
                                  item.purchaseOrderItemId ? (
                                  <Loader2Icon className="h-4 w-4 animate-spin" />
                                ) : item.purchaseOrderItemId ? (
                                  <TrashIcon className="h-4 w-4" />
                                ) : (
                                  <XIcon className="h-4 w-4" />
                                )}
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>

                    <TableFooter>
                      {taxAmount || whtAmount ? (
                        <TableRow>
                          <TableCell
                            colSpan={3}
                            className="text-right font-medium"
                          >
                            Amount:
                          </TableCell>
                          <TableCell className="font-medium">
                            {taxAmount.toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}{" "}
                            {po.currency}
                          </TableCell>
                          <TableCell />
                        </TableRow>
                      ) : null}
                      {taxAmount ? (
                        <TableRow>
                          <TableCell
                            colSpan={3}
                            className="text-right font-medium"
                          >
                            VAT:
                          </TableCell>
                          <TableCell className="font-medium">
                            {taxAmount.toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}{" "}
                            {po.currency}
                          </TableCell>
                          <TableCell />
                        </TableRow>
                      ) : null}
                      {whtAmount ? (
                        <TableRow>
                          <TableCell
                            colSpan={3}
                            className="text-right font-medium"
                          >
                            WHT:
                          </TableCell>
                          <TableCell className="font-medium">
                            {(whtAmount * -1).toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}{" "}
                            {po.currency}
                          </TableCell>
                          <TableCell />
                        </TableRow>
                      ) : null}
                      <TableRow>
                        <TableCell
                          colSpan={3}
                          className="text-right font-medium"
                        >
                          {ammountError ? (
                            <small className="text-red-500">
                              Your total should be higher or equal to the paid
                              installments
                            </small>
                          ) : null}{" "}
                          Total Amount:
                        </TableCell>
                        <TableCell className="font-bold text-primary">
                          {totalAmount.toLocaleString("en-US", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}{" "}
                          {po.currency}
                        </TableCell>
                        <TableCell />
                      </TableRow>
                    </TableFooter>
                  </Table>
                </div>
              </SimpleCard>
            </TabsContent>

            <TabsContent value="payments" className="space-y-6">
              <SimpleCard
                title="Payment Schedule"
                description={
                  installmentError ? (
                    <p className="text-destructive">
                      Warning: Your payment total (
                      {payments
                        .reduce((acc, p) => acc + (p.amount || 0), 0)
                        .toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      ) doesn't match the order total (
                      {totalAmount.toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                      ), difference:{" "}
                      {(
                        totalAmount -
                        payments.reduce((acc, p) => acc + (p.amount || 0), 0)
                      ).toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}{" "}
                      {po.currency}
                      {/* {payments.some((p) => p.status !== "idle") ? (
                        <span className="text-red-500">
                          , you cannot change the payment amount after some
                          payments have been sent to review or paid
                        </span>
                      ) : null} */}
                    </p>
                  ) : null
                }
              >
                <div className="mb-6 flex items-center space-x-2">
                  <div className="flex-1">
                    <Label
                      htmlFor="paymentType"
                      className="text-base font-medium"
                    >
                      Payment Type
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Choose between a single payment or multiple installments{" "}
                      <small className="text-red-500">
                        {payments.some((p) => p.status !== "idle")
                          ? "You cannot change payment type after some payments have been sent to review or paid"
                          : null}
                      </small>
                    </p>
                  </div>

                  <div className="flex items-center gap-4">
                    <Label htmlFor="paymentType" className="cursor-pointer">
                      Installments
                    </Label>
                    <Switch
                      id="paymentType"
                      disabled={
                        purchaseOrder?.isPaid ||
                        payments.some((p) => p.status !== "idle") ||
                        !!purchaseOrder
                      }
                      checked={po.paymentType === "installment"}
                      onCheckedChange={(checked) => {
                        if (purchaseOrder) {
                          setToUpdate(true);
                        }
                        if (!checked) {
                          setPo({ ...po, paymentType: "cash" });
                          setPayments([
                            {
                              amount: totalAmount,
                              date: new Date(),
                              description: "Full Payment",
                              percentage: 100,
                              status: "idle",
                            },
                          ]);
                        } else {
                          setPo({ ...po, paymentType: "installment" });
                          setPayments([
                            {
                              amount: totalAmount * 0.5,
                              date: new Date(),
                              description: "Installment 1",
                              percentage: 50,
                              status: "idle",
                            },
                            {
                              amount: totalAmount * 0.5,
                              date: new Date(
                                new Date().setMonth(new Date().getMonth() + 1),
                              ),
                              description: "Installment 2",
                              percentage: 50,
                              status: "idle",
                            },
                          ]);
                        }
                      }}
                    />
                  </div>
                </div>

                {po.paymentType === "cash" ? (
                  <div className="rounded-md border border-border p-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="singlePaymentAmount">
                          Payment Amount
                        </Label>
                        <div className="flex items-center rounded-md border bg-muted/50 px-3 py-2">
                          <span className="mr-2 text-muted-foreground">
                            {po.currency}
                          </span>
                          {/* <DollarSignIcon className="mr-2 h-4 w-4 text-muted-foreground" /> */}
                          <span>
                            {totalAmount.toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="singlePaymentDate">Due Date</Label>
                        <DatePicker
                          disabled={purchaseOrder?.isPaid}
                          date={payments[0]?.date}
                          onSelect={(date) => {
                            if (purchaseOrder) {
                              setToUpdate(true);
                            }
                            console.log(date);

                            setPayments([
                              {
                                ...payments[0],
                                date,
                                changed: !!payments[0]?.PurchaseOrderPaymentId,
                              },
                            ]);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-4 flex justify-end">
                      <Button
                        disabled={purchaseOrder?.isPaid}
                        onClick={() => {
                          if (purchaseOrder) {
                            setToUpdate(true);
                          }
                          setPayments([
                            ...payments,
                            {
                              date: new Date(
                                new Date().setMonth(
                                  new Date().getMonth() + payments.length,
                                ),
                              ),
                              amount: 0,
                              status: "idle",
                              percentage: 0,
                              description: `Installment ${payments.length + 1}`,
                            },
                          ]);
                        }}
                        className="flex items-center gap-1"
                      >
                        <PlusIcon className="mr-1 h-4 w-4" />
                        Add Installment
                      </Button>
                    </div>

                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Description</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Percentage</TableHead>
                            <TableHead>Due Date</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Timeline</TableHead>
                            <TableHead className="w-[100px]">Actions</TableHead>
                          </TableRow>
                        </TableHeader>

                        <TableBody>
                          {payments.map((payment, index) => {
                            const deleteDisabled =
                              payment.status !== "idle" ||
                              payments.length <= 2 ||
                              !!payment?.paidAt ||
                              (deletePaymentIsPending &&
                                deletePaymentVariables?.PurchaseOrderPaymentId ===
                                  payment?.PurchaseOrderPaymentId);

                            return (
                              <TableRow key={index}>
                                <TableCell>
                                  <Input
                                    disabled={
                                      purchaseOrder?.isPaid || !!payment?.paidAt
                                    }
                                    placeholder="Payment description"
                                    value={payment.description || ""}
                                    onChange={(e) => {
                                      if (purchaseOrder) {
                                        setToUpdate(true);
                                      }
                                      updatePayment(index, {
                                        changed:
                                          !!payment.PurchaseOrderPaymentId,
                                        description: e.target.value,
                                      });
                                    }}
                                  />
                                </TableCell>

                                <TableCell>
                                  <Input
                                    disabled={
                                      purchaseOrder?.isPaid || !!payment?.paidAt
                                    }
                                    type="number"
                                    placeholder="0.00"
                                    value={payment.amount || ""}
                                    onChange={(e) => {
                                      if (purchaseOrder) {
                                        setToUpdate(true);
                                      }
                                      const value = parseFloat(e.target.value);
                                      if (isNaN(value) || value >= 0) {
                                        const totalPaymentsExcludingCurrent =
                                          payments
                                            .filter((_, i) => i !== index)
                                            .reduce(
                                              (acc, p) => acc + (p.amount || 0),
                                              0,
                                            );

                                        if (
                                          isNaN(value) ||
                                          totalPaymentsExcludingCurrent +
                                            value <=
                                            totalAmount
                                        ) {
                                          updatePayment(index, {
                                            changed:
                                              !!payment.PurchaseOrderPaymentId,
                                            amount: isNaN(value) ? 0 : value,
                                            percentage: isNaN(value)
                                              ? 0
                                              : parseFloat(
                                                  (
                                                    (value / totalAmount) *
                                                    100
                                                  ).toFixed(2),
                                                ),
                                          });
                                        }
                                      }
                                    }}
                                  />
                                </TableCell>

                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    <span>{payment.percentage || 0}%</span>
                                  </div>
                                </TableCell>

                                <TableCell>
                                  <DatePicker
                                    disabled={
                                      purchaseOrder?.isPaid || !!payment?.paidAt
                                    }
                                    date={payment.date}
                                    onSelect={(date) => {
                                      if (purchaseOrder) {
                                        setToUpdate(true);
                                      }
                                      updatePayment(index, {
                                        date,
                                        changed:
                                          !!payment?.PurchaseOrderPaymentId,
                                      });
                                    }}
                                  />
                                </TableCell>

                                <TableCell>
                                  <div className="flex items-center">
                                    {payment.status == "paid" ? (
                                      <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                        <CheckIcon className="mr-1 h-3 w-3" />
                                        Paid
                                      </span>
                                    ) : (
                                      <span className="inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800">
                                        {payment.status}
                                      </span>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <HoverCard>
                                    <HoverCardContent>
                                      <div>
                                        Reviewed At:{" "}
                                        {payment.reviewedAt?.toLocaleDateString()}
                                      </div>
                                      <div>
                                        Approved At:{" "}
                                        {payment.approvedAt?.toLocaleDateString()}
                                      </div>
                                      <div>
                                        Paid At:{" "}
                                        {payment.paidAt?.toLocaleDateString()}
                                      </div>
                                    </HoverCardContent>
                                    <HoverCardTrigger>
                                      <History />
                                    </HoverCardTrigger>
                                  </HoverCard>
                                </TableCell>
                                <TableCell>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    disabled={
                                      deleteDisabled || purchaseOrder?.isPaid
                                    }
                                    onClick={() => {
                                      if (purchaseOrder) {
                                        setToUpdate(true);
                                      }
                                      if (payment?.PurchaseOrderPaymentId) {
                                        deletePayment({
                                          purchaseOrderId:
                                            purchaseOrder?.purchaseOrderId ||
                                            null,
                                          PurchaseOrderPaymentId:
                                            payment.PurchaseOrderPaymentId,
                                        });
                                      } else {
                                        setPayments(
                                          payments.filter(
                                            (_, i) => i !== index,
                                          ),
                                        );
                                      }
                                    }}
                                  >
                                    {deletePaymentIsPending &&
                                    deletePaymentVariables?.PurchaseOrderPaymentId ===
                                      payment?.PurchaseOrderPaymentId ? (
                                      <Loader2Icon className="h-4 w-4 animate-spin" />
                                    ) : payment?.PurchaseOrderPaymentId ? (
                                      <TrashIcon className="h-4 w-4" />
                                    ) : (
                                      <XIcon className="h-4 w-4" />
                                    )}
                                  </Button>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>

                        <TableFooter>
                          <TableRow>
                            <TableCell
                              colSpan={6}
                              className="text-right font-medium"
                            >
                              Total Payments:
                            </TableCell>
                            <TableCell
                              className={`font-bold ${installmentError ? "text-destructive" : "text-primary"}`}
                            >
                              {payments
                                .reduce((acc, p) => acc + (p.amount || 0), 0)
                                .toLocaleString("en-US", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}{" "}
                              {po.currency}
                            </TableCell>
                          </TableRow>
                        </TableFooter>
                      </Table>
                    </div>
                  </>
                )}
              </SimpleCard>
            </TabsContent>

            <TabsContent value="approvals" className="space-y-6">
              <SimpleCard title="Approval Workflow">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="userReviewId">
                        <span className="flex items-center gap-2">
                          <UserIcon className="h-4 w-4" />
                          Review By{" "}
                          <small className="text-red-500">
                            {payments?.some(
                              (p) =>
                                p.status !== "idle" && p.status !== "rejected",
                            ) ||
                            purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.some(
                              (p) => !!p?.paidAt,
                            )
                              ? "You can't change this during the approval process or if some payments have been paid"
                              : ""}
                          </small>
                        </span>
                      </Label>
                      <Select
                        disabled={
                          payments?.some(
                            (p) =>
                              p.status !== "idle" && p.status !== "rejected",
                          ) ||
                          purchaseOrder?.PurchaseOrderDetails?.PurchaseOrderPayments?.some(
                            (p) => !!p?.paidAt,
                          )
                        }
                        value={po.userReviewId || undefined}
                        onValueChange={(value) => {
                          if (purchaseOrder) {
                            setToUpdate(true);
                          }
                          setPo({ ...po, userReviewId: value });
                        }}
                      >
                        <SelectTrigger id="userReviewId">
                          <SelectValue placeholder="Select Reviewer" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Select Reviewer</SelectLabel>
                            {users
                              ?.filter(
                                (u) =>
                                  !["admin", "ceo"].includes(u.role.role) &&
                                  !u.username?.toLowerCase()?.includes("azzam"),
                              )
                              .map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.username || user.email || ""}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="userApproveId">
                        <span className="flex items-center gap-2">
                          <CheckIcon className="h-4 w-4" />
                          Approve By
                        </span>
                      </Label>
                      <Select
                        disabled
                        value={po.userApproveId || undefined}
                        // onValueChange={(value) => {
                        //   setPo({ ...po, userApproveId: value });
                        // }}
                      >
                        <SelectTrigger id="userApproveId">
                          <SelectValue placeholder="Select Approver" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Select Approver</SelectLabel>
                            {users
                              ?.filter((u) => u.role.role === "ceo")
                              .map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.username || user.email || ""}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="rounded-md border border-border bg-muted/20 p-4">
                    <h3 className="mb-3 font-medium">Attachments</h3>

                    <div className="flex flex-wrap gap-2">
                      <AttachmentUploader
                        attachments={attachments}
                        setAttachments={setAttachments}
                      />
                      <div className="flex flex-wrap gap-5">
                        {[
                          ...(po?.purchaseOrderAttachments || []),
                          ...attachments,
                        ].map((a, i) => {
                          return (
                            <div
                              key={i + "attachment"}
                              className="group relative flex items-center justify-center rounded-md border border-dotted"
                            >
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <span className="absolute right-0 top-0 hidden size-5 cursor-pointer items-center justify-center rounded-full bg-red-500 group-hover:flex">
                                    <X size={10} className="text-white" />
                                  </span>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      Delete Attachment
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete this
                                      attachment?
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>
                                      Cancel
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => {
                                        if ("Attachment" in a)
                                          deleteAttachmentHandler({
                                            attachmentId: a.attachmentId,
                                            key: a.Attachment.key || "",
                                            purchaseOrderAttachmentId:
                                              a.purchaseOrderAttachmentId,
                                          });
                                        else deleteAttachmentUTHandler([a.key]);
                                      }}
                                      className="bg-red-600"
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                              <ImageViewer
                                w={100}
                                h={100}
                                attachment={
                                  "Attachment" in a ? a?.Attachment : a
                                }
                                onDel={() => {}}
                                className="max-h-[100px] object-contain"
                              />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </SimpleCard>
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex items-center justify-between">
            <Button
              disabled={toUpdate}
              variant="outline"
              onClick={() => router.push("/purchaseOrder")}
            >
              Cancel
            </Button>

            <div className="flex gap-2"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
