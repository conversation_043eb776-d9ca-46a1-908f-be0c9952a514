import { api } from "@/trpc/server";

import ProcessComponent from "./_components/processComponent";
import ChatBubble from "@/app/(app)/_components/chatBubble";

const Page = async ({ params }: { params: { ids: string } }) => {
  const [purchaseOrderId, PurchaseOrderPaymentId] = params.ids.split(
    "_",
  ) as string[];

  const purchaseOrder = await api.purchaseOrder.getAllPurchaseOrderDataView({
    purchaseOrderId: purchaseOrderId || "",
    PurchaseOrderPaymentId: purchaseOrderId || "",
  });
  return (
    <>
      {purchaseOrderId && (
        <ChatBubble
          defualtOpen={false}
          purchaseOrderId={purchaseOrderId}
        />
      )}
      <ProcessComponent
        purchaseOrder={purchaseOrder as any}
        PurchaseOrderPaymentId={PurchaseOrderPaymentId}
      />
    </>
  );
};

export default Page;
