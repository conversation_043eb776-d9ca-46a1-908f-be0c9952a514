"use client";
import React, { useEffect, useMemo, useState } from "react";
import { notFound, useParams } from "next/navigation";
import { api } from "@/trpc/react";
import Image from "next/image";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle, XCircle } from "lucide-react";
import PuchaseOrderView from "@/app/_components/puchaseOrderView";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import { useChannel } from "ably/react";
import { toast } from "react-toastify";
import { PurchaseOrderI } from "@/lib/types";
import { PaymentStatus } from "@prisma/client";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ProcessComponent = ({
  purchaseOrder,
  PurchaseOrderPaymentId,
}: {
  purchaseOrder: PurchaseOrderI;
  PurchaseOrderPaymentId: string | undefined;
}) => {
  const { channel } = useChannel("notifications");

  const [form, setForm] = useState({ comment: "" });
  const [showRejectReason, setShowRejectReason] = useState(false);
  const [transferAccountantId, setTransferAccountantId] = useState("");
  const { data: users } = api.user.getAll.useQuery();
  const { mutate } = api.purchaseOrder.reviewAndPayPayment.useMutation({
    onSuccess: (res) => {
      if (res) {
        res.notifications.forEach((notification) => {
          channel.publish(
            "po-notifi",
            JSON.stringify({
              ...notification,
            }),
          );
        });
      }
      router.refresh();
      toast.success("Thank you for your review");
    },
  });
  const router = useRouter();
  const session = useSession();

  const userMatchStatus = (status: PaymentStatus) => {
    if (
      status == "toReview" &&
      session.data?.user.id === purchaseOrder.userReviewId
    ) {
      return {
        action: "review",
      };
    } else if (
      status == "toApprove" &&
      session.data?.user.id === purchaseOrder.userApproveId
    ) {
      return {
        action: "approve",
      };
    } else if (
      status == "toAudit" &&
      (session.data?.user.role.role === "auditor" ||
        session.data?.user.id === purchaseOrder.userAuditId)
    ) {
      return {
        action: "audit",
      };
    } else if (
      status == "toPay" &&
      session.data?.user.role.role === "accountantManager"
    ) {
      return {
        action: "transfer-pay",
      };
    } else if (
      status == "transferred" &&
      session.data?.user.role.role.includes("accountant")
    ) {
      return {
        action: "pay",
      };
    } else {
      return null;
    }
  };

  const actionDetail = useMemo(() => {
    if (
      purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments.every(
        (p) => p.status === "paid",
      )
    ) {
      router.push(`/purchaseOrder/process/${purchaseOrder.purchaseOrderId}`);
    }
    if (!PurchaseOrderPaymentId) {
      const pos =
        purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments.filter(
          (p) =>
            p.status !== "paid" &&
            p.status !== "idle" &&
            p.PurchaseOrderPaymentId !== PurchaseOrderPaymentId,
        );

      for (const pop of pos) {
        if (userMatchStatus(pop.status)) {
          router.push(
            `/purchaseOrder/process/${purchaseOrder.purchaseOrderId}_${pop.PurchaseOrderPaymentId}`,
          );
          break;
        }
      }
      return null;
    }
    if (session.status === "unauthenticated" || session.status === "loading") {
      return null;
    }

    const poToWork =
      purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments.find(
        (po) => po.PurchaseOrderPaymentId === PurchaseOrderPaymentId,
      );
    // const
    if (!poToWork) return null;

    const action = userMatchStatus(poToWork.status);
    if (!action) {
      const remainingPayments =
        purchaseOrder.PurchaseOrderDetails.PurchaseOrderPayments.filter(
          (p) =>
            p.status !== "paid" &&
            p.status !== "idle" &&
            p.PurchaseOrderPaymentId !== PurchaseOrderPaymentId,
        );

      for (const pop of remainingPayments) {
        if (userMatchStatus(pop.status)) {
          toast(`You have ${remainingPayments.length} payments to work on`, {
            toastId: "customId",
          });
          router.push(
            `/purchaseOrder/process/${purchaseOrder.purchaseOrderId}_${pop.PurchaseOrderPaymentId}`,
          );
          break;
        }
      }
    }
    if (!action) return null;
    return {
      payment: poToWork,
      status: poToWork.status,
      ...action,
    };
  }, [purchaseOrder, PurchaseOrderPaymentId, session]);

  const handleUpdatePaymentState = (data: {
    approved: boolean;
    transfer?: boolean;
  }) => {
    if (!actionDetail || !PurchaseOrderPaymentId) return;
    const statsArr = ["toReview", "toApprove", "toAudit", "toPay"];
    const payload = {
      //   purchaseOrder,
      purchaseOrderId: purchaseOrder.purchaseOrderId,
      PurchaseOrderPaymentId,
      currentStatus: actionDetail?.status,
      nextStatus: data.transfer
        ? "transferred"
        : data.approved
          ? actionDetail?.status == "toPay" ||
            actionDetail?.status == "transferred"
            ? "paid"
            : statsArr[statsArr.indexOf(actionDetail.status) + 1] || null
          : "rejected",
      comment: form.comment || "",
      transfer: transferAccountantId,
    };

    mutate(payload as any);
  };

  //   if (!purchaseOrder && !isLoading) return notFound();
  // FIXME: add is paid or draft flags
  return (
    <div className="flex flex-col justify-between">
      <div
        className={`${!actionDetail ? "grid grid-cols-1" : "gird-cols-1 grid gap-3 p-3 md:grid-cols-2"} relative`}
      >
        <PuchaseOrderView
          purchaseOrder={purchaseOrder as any}
          PurchaseOrderPaymentId={PurchaseOrderPaymentId}
        />
        {actionDetail && (
          <Card className="sticky top-5 h-fit w-full print:hidden">
            <CardHeader>
              <CardTitle className="capitalize">
                {actionDetail?.action} Purchase Order
              </CardTitle>
              <CardDescription>
                {actionDetail?.action === "pay" ||
                actionDetail?.action === "transfer-pay"
                  ? "add attachments and pay this payment" +
                      actionDetail?.action ===
                    "transfer-pay"
                    ? " or transfer this payment to other accountant"
                    : ""
                  : actionDetail?.action +
                    " and approve or reject this purchase order"}
              </CardDescription>
            </CardHeader>
            {/* for rejection */}
            <CardContent>
              <div className="space-y-4">
                {showRejectReason && (
                  <div className="space-y-2">
                    <label htmlFor="reason" className="text-sm font-medium">
                      Rejection Reason
                    </label>
                    <Textarea
                      onChange={(e) => {
                        setForm({ ...form, comment: e.target.value });
                      }}
                      id="reason"
                      placeholder="Please provide a reason for rejection..."
                      className="min-h-[100px]"
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              {actionDetail?.status == "toPay" ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant={"outline"}>Transfer To Accountant</Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        Transfer purchase order
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        Please select the accountant to transfer this purchase
                        order to.
                      </AlertDialogDescription>
                    </AlertDialogHeader>

                    <div className="mt-3 grid w-full gap-1.5">
                      <Label htmlFor="message" className="text-black">
                        Accountants
                      </Label>
                      <Select
                        value={transferAccountantId || undefined}
                        onValueChange={(v: string) => {
                          setTransferAccountantId(v);
                        }}
                      >
                        <SelectTrigger className="">
                          <SelectValue placeholder="Select Accountant" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {users?.map((user) => {
                              if (user.role.role === "accountant") {
                                return (
                                  <SelectItem key={user.id} value={user.id}>
                                    {user.username}
                                  </SelectItem>
                                );
                              }
                            })}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>

                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => {
                          handleUpdatePaymentState({
                            approved: false,
                            transfer: true,
                          });
                        }}
                      >
                        Transfer To Accountant
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : actionDetail?.status !== "transferred" ||
                session.data?.user.role.role.includes("accountant") ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button className="border border-input bg-background text-black hover:bg-red-700 hover:text-white">
                      Reject
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Reject purchase order</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to reject this purchase order?
                      </AlertDialogDescription>
                    </AlertDialogHeader>

                    <div className="mt-3 grid w-full gap-1.5">
                      <Label htmlFor="message" className="text-black">
                        Rejection reason
                      </Label>
                      <Textarea
                        onChange={(e) => {
                          setForm({ ...form, comment: e.target.value });
                        }}
                        placeholder="Type your message here."
                        id="message"
                      />
                    </div>

                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        disabled={!form.comment}
                        onClick={() => {
                          handleUpdatePaymentState({
                            approved: false,
                          });
                        }}
                        className="bg-red-600"
                      >
                        Reject
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : null}
              <Button
                onClick={() => handleUpdatePaymentState({ approved: true })}
                className="flex items-center gap-2"
              >
                {actionDetail?.status == "toPay" ||
                actionDetail?.status === "transferred" ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Mark As Paid
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Approve
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProcessComponent;
