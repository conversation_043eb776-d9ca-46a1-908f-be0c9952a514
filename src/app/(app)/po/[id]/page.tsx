import { Textarea } from "@/components/ui/textarea";
import { db } from "@/server/db";
import React from "react";
import { Button } from "react-day-picker";

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const res = await db.purchaseOrder.findFirst({
    where: {
      purchaseOrderId: id,
    },
    include: {
      PurchaseOrderDetails: {
        include: {
          PurchaseOrderPayments: true,
          PurchaseOrderItems: true,
        },
      },
    },
  });
  return (
    <div className="">
      <Textarea rows={50}>{JSON.stringify(res, null, 2)}</Textarea>
      {/* <Button
        onClick={() => {
          // if (res) navigator.clipboard.writeText(JSON.stringify(res));
        }}
      >
        copy
      </Button> */}
    </div>
  );
};

export default page;
