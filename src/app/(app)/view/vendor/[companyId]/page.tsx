"use client";
import React from "react";
import { api } from "@/trpc/react";
import { useParams } from "next/navigation";
import { BoxIcon, Edit, Paperclip } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Building2,
  Phone,
  Mail,
  MapPin,
  User,
  FileText,
  CreditCard,
  Calendar,
  Info,
} from "lucide-react";
import Image from "next/image";
import { format } from "date-fns";
import Link from "next/link";
import { useSession } from "next-auth/react";

const Page = () => {
  const { data: session } = useSession();

  const { companyId } = useParams<{ companyId: string }>();
  const { data: company, isLoading } = api.company.getOne.useQuery({
    companyId,
  });

  if (isLoading) return <div className="p-5">Loading...</div>;
  if (!company)
    return (
      <div className="flex h-full w-full flex-col items-center justify-center text-5xl">
        <BoxIcon size={60} />
        Vendor not found
      </div>
    );
  // Format dates
  const formatDate = (date: Date) => {
    return format(new Date(date), "PPP");
  };

  return (
    <div className="m-5 space-y-6 p-5">
      {/* Company Header */}
      <Card className="overflow-hidden">
        <div className="bg-gradient-to-r from-zinc-50 to-amber-50 p-6 dark:from-zinc-950 dark:to-amber-950">
          <div className="flex flex-col items-center gap-6 md:flex-row">
            <Building2 size={40} />
            <div className="text-center md:text-left">
              <h1 className="text-2xl font-bold">{company.companyName}</h1>
              <p className="text-muted-foreground">{company.legalName}</p>
              <div className="mt-2">
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  {company.type}
                </Badge>
              </div>
            </div>
          </div>
          {session?.user.role.role &&
          ["admin", "accountant"].includes(session?.user.role.role || "") ? (
            <Link
              href={`/manage/companies/${company.companyId}`}
              className="float-right h-full align-middle"
            >
                <Edit size={20} />
            </Link>
          ) : null}
        </div>
      </Card>

      {/* Company Details */}
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-4 grid grid-cols-3">
          <TabsTrigger value="general">General Info</TabsTrigger>
          <TabsTrigger value="contact">Contact Details</TabsTrigger>
          <TabsTrigger value="payment">Payment Info</TabsTrigger>
        </TabsList>

        {/* General Info Tab */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info size={20} />
                General Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <InfoItem
                  icon={<Building2 className="h-5 w-5 text-muted-foreground" />}
                  label="Vendor Name"
                  value={company.companyName}
                />
                <InfoItem
                  icon={<Building2 className="h-5 w-5 text-muted-foreground" />}
                  label="Legal Name"
                  value={company.legalName}
                />
                <InfoItem
                  icon={<FileText className="h-5 w-5 text-muted-foreground" />}
                  label="Sales Tax Number"
                  value={company.salesTaxNumber || "N/A"}
                />
                <InfoItem
                  icon={<FileText className="h-5 w-5 text-muted-foreground" />}
                  label="National ID"
                  value={company.nationalId}
                />
                <InfoItem
                  icon={<FileText className="h-5 w-5 text-muted-foreground" />}
                  label="Description"
                  value={company.description || "No description provided"}
                />
                <InfoItem
                  icon={<Calendar className="h-5 w-5 text-muted-foreground" />}
                  label="Created At"
                  value={formatDate(company.createdAt)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Details Tab */}
        <TabsContent value="contact">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone size={20} />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <InfoItem
                  icon={<Phone className="h-5 w-5 text-muted-foreground" />}
                  label="Company Phone"
                  value={company.companyPhone}
                />
                <InfoItem
                  icon={<Mail className="h-5 w-5 text-muted-foreground" />}
                  label="Email"
                  value={company.email}
                />
                <InfoItem
                  icon={<MapPin className="h-5 w-5 text-muted-foreground" />}
                  label="Address"
                  value={company.address}
                />
                <InfoItem
                  icon={<User className="h-5 w-5 text-muted-foreground" />}
                  label="Contact Person"
                  value={company.contactPerson}
                />
                <InfoItem
                  icon={<Phone className="h-5 w-5 text-muted-foreground" />}
                  label="Contact Phone"
                  value={company.contactPhone}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Info Tab */}
        <TabsContent value="payment">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard size={20} />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <InfoItem
                  icon={
                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                  }
                  label="Payment Method"
                  value={company.paymentMethod || "No payment method provided"}
                />
                {company.paymentMethod === "cheque" && (
                  <InfoItem
                    icon={
                      <FileText className="h-5 w-5 text-muted-foreground" />
                    }
                    label="Name on Cheque"
                    value={company.nameOnCheque || "N/A"}
                  />
                )}
                {company.paymentMethod === "bankTransfer" && (
                  <InfoItem
                    icon={
                      <CreditCard className="h-5 w-5 text-muted-foreground" />
                    }
                    label="IBAN"
                    value={company.iban || "N/A"}
                  />
                )}
                {company.paymentMethod === "bankTransfer" && (
                  <InfoItem
                    icon={<User className="h-5 w-5 text-muted-foreground" />}
                    label="Account Name"
                    value={company.accountName || "N/A"}
                  />
                )}
                {company.paymentMethod === "bankTransfer" && (
                  <InfoItem
                    icon={
                      <FileText className="h-5 w-5 text-muted-foreground" />
                    }
                    label="Swift Code"
                    value={company.swiftCode || "N/A"}
                  />
                )}
                {company.paymentMethod === "CLIQ" && (
                  <InfoItem
                    icon={
                      <CreditCard className="h-5 w-5 text-muted-foreground" />
                    }
                    label="Cliq"
                    value={company.cliq || "N/A"}
                  />
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Attachments Gallery */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText size={20} />
            Vendor Attachments
          </CardTitle>
        </CardHeader>
        <CardContent>
          {company.CompanyAttachments.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
              {company.CompanyAttachments.map((attachment) =>
                attachment.Attachment.type.includes("image") ? (
                  <Link
                    target="_blank"
                    href={attachment.Attachment.url}
                    className=""
                  >
                    <div
                      key={attachment.companyAttachmentId}
                      className="group relative"
                    >
                      <div className="relative aspect-square overflow-hidden rounded-lg border">
                        <Image
                          src={attachment.Attachment.url || "/placeholder.svg"}
                          alt={attachment.Attachment.name || "Attachment"}
                          fill
                          className="object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                      <div className="mt-2 truncate text-center text-sm text-muted-foreground">
                        {attachment.Attachment.name}
                      </div>
                    </div>
                  </Link>
                ) : (
                  <Link
                    href={attachment.Attachment.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex size-24 w-32 items-center justify-center rounded-md border border-input bg-background font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                  >
                    <Paperclip
                      size={30}
                      className="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 group-hover:visible group-hover:absolute"
                    />
                    <p className="visible text-center text-sm group-hover:invisible">
                      {attachment.Attachment.name}
                    </p>
                  </Link>
                ),
              )}
            </div>
          ) : (
            <p className="py-4 text-center text-muted-foreground">
              No attachments available
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Helper component for displaying info items
function InfoItem({
  icon,
  label,
  value,
}: {
  icon: React.ReactNode;
  label: string;
  value: string;
}) {
  return (
    <div className="flex flex-col space-y-1">
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
        {icon}
        {label}
      </div>
      <div className="font-medium">{value}</div>
      <Separator className="mt-2" />
    </div>
  );
}

export default Page;
