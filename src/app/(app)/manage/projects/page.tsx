"use client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { But<PERSON> } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import { api } from "@/trpc/react";
import { toast } from "react-toastify";
import { Info, Trash2 } from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";

export default function Page() {
  const {
    data: projects,
    refetch,
    isLoading,
  } = api.project.getDashboard.useQuery();
  // console.log(projects[0]?._count.PurchaseOrderDetails);

  const updateProject = api.project.update.useMutation({
    onSuccess: () => {
      toast("Project Updated Successfully", {
        type: "success",
        position: "bottom-right",
      });
      refetch();
    },
    onError: () => {
      toast("Something went wrong!", {
        type: "error",
        position: "bottom-right",
      });
    },
  });
  const deleteProject = api.project.delete.useMutation({
    onSuccess: () => {
      toast("Project Deleted Successfully", {
        type: "success",
        position: "bottom-right",
      });
      refetch();
    },
    onError: () => {
      toast("Something went wrong!", {
        type: "error",
        position: "bottom-right",
      });
    },
  });

  // Search and pagination states
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and sort projects:
  // - Filter by project name (case-insensitive)
  // - Sort so that open projects appear first and closed projects come last
  const filteredProjects = useMemo(() => {
    if (!projects) return [];
    const lowerSearch = searchTerm.toLowerCase();
    const filtered = projects.filter((pro) =>
      pro.projectName.toLowerCase().includes(lowerSearch),
    );
    return filtered
      .slice()
      .sort((a, b) => (a.closed === b.closed ? 0 : a.closed ? 1 : -1));
  }, [projects, searchTerm]);

  // Calculate total pages (20 items per page)
  const totalPages = Math.ceil(filteredProjects.length / 20);

  // Slice filtered projects for current page
  const paginatedProjects = useMemo(() => {
    const startIndex = (currentPage - 1) * 20;
    return filteredProjects.slice(startIndex, startIndex + 20);
  }, [filteredProjects, currentPage]);

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Create new Project?
            <Link href="/manage/projects/new" className="underline">
              <Button variant={"outline"}>Create new project</Button>
            </Link>
          </CardTitle>
        </CardHeader>
      </Card>
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <CardTitle className="mb-2 md:mb-0">Manage Projects</CardTitle>
            <CardDescription>
              Update the project by clicking on the name and changing it.
            </CardDescription>
          </div>
          <div className="mt-2 md:mt-0">
            <Input
              type="text"
              placeholder="Search projects"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to page 1 on new search
              }}
              className="max-w-xs"
            />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableCaption>
              Showing {paginatedProjects.length} of {filteredProjects.length}{" "}
              projects
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Project name</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="text-center">
                  <HoverCard>
                    <HoverCardTrigger className="flex items-center justify-center gap-2">
                      status <Info size={10} />
                    </HoverCardTrigger>
                    <HoverCardContent>
                      <small>
                        This indicates if you can create more purchase orders
                        with this project or not
                      </small>
                    </HoverCardContent>
                  </HoverCard>
                </TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedProjects.map((pro) => (
                <TableRow key={pro.projectId}>
                  <TableCell
                    className="font-medium"
                    contentEditable
                    onBlur={(e) => {
                      if (e.target.innerText !== pro.projectName)
                        updateProject.mutate({
                          projectId: pro.projectId,
                          projectName: e.target.innerText,
                          closed: pro.closed,
                        });
                    }}
                  >
                    {pro.projectName}
                  </TableCell>
                  <TableCell className="font-medium">
                    {pro.createdAt.toDateString()}
                  </TableCell>
                  <TableCell
                    onClick={() => {
                      updateProject.mutate({
                        projectId: pro.projectId,
                        projectName: pro.projectName,
                        closed: !pro.closed,
                      });
                    }}
                    className="cursor-pointer text-center font-medium"
                  >
                    <Button
                      className="w-full"
                      variant={pro.closed ? "destructive" : "secondary"}
                      size={"sm"}
                    >
                      {pro.closed ? "closed" : "open"}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Trash2
                      onClick={() => {
                        if (pro.projectId && !pro._count.PurchaseOrderDetails)
                          deleteProject.mutate({ projectId: pro.projectId });
                      }}
                      className={`${pro._count.PurchaseOrderDetails ? "text-gray-500" :"cursor-pointer text-red-500"}`}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            {(isLoading || !projects?.length) && (
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    {isLoading ? "Loading..." : "No projects found"}
                  </TableCell>
                </TableRow>
              </TableFooter>
            )}
          </Table>
          {/* Pagination Controls */}
          <div className="mt-4 flex items-center justify-between">
            <Button
              variant="outline"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => prev - 1)}
            >
              Previous
            </Button>
            <span>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant="outline"
              disabled={currentPage === totalPages || totalPages === 0}
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
