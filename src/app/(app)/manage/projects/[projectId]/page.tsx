"use client";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { api } from "@/trpc/react";
import { toast } from "react-toastify";
import { UploadButton, UploadDropzone } from "@/components/uploadThings";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Company, Project } from "@prisma/client";
import { useEffect, useMemo, useState } from "react";
import { redirect, useParams, useRouter } from "next/navigation";
import { ClientUploadedFileData } from "uploadthing/types";
import Image from "next/image";
import ImageViewer from "@/components/ui/ImageViewer";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import SelectCreate from "@/app/(app)/_components/select-create";
type ProT = Partial<Project>;

export default function Page() {
  const router = useRouter();
  const { projectId } = useParams<{ projectId: string }>();

  const {
    data: project,
    refetch,
    isLoading,
  } = api.project.getOne.useQuery({
    projectId,
  });

  const [projectToEdit, setProjectToEdit] = useState<ProT>(
    (project as ProT) || {
      alternatives: [],
      projectName: "",
      closed: false,
    },
  );

  useEffect(() => {
    if (project) {
      setProjectToEdit({ ...project } as ProT);
    }
  }, [project]);

  const { mutate: updateProject } = api.project.update.useMutation();
  const { mutate: createProject } = api.project.create.useMutation();

  const updateProjectHandler = () => {
    if (!project) return;
    if (projectToEdit.projectName?.trim() === "")
      return toast.error("Project name is required");
    updateProject(
      {
        projectId: projectId,
        projectName: projectToEdit.projectName || "",
        closed: !!projectToEdit?.closed,
      },
      {
        onSuccess: () => {
          toast.success("Company Updated Successfully");
          refetch();
        },
      },
    );
  };

  const createProjectHandler = () => {
    if (projectToEdit.projectName?.trim() === "")
      return toast.error("Project name is required");
    createProject(
      {
        projectName: projectToEdit.projectName || "",
      },
      {
        onSuccess: (res) => {
          router.push(`/manage/projects/${res.projectId}`);
        },
      },
    );
  };

  const onClickHandler = () => {
    if (project) {
      updateProjectHandler();
    } else {
      createProjectHandler();
    }
  };

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between">
            {project ? "Update project info" : "Create new project"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="projectName">Project Name</Label>
            <Input
              id="projectName"
              type="text"
              value={projectToEdit.projectName}
              onChange={(e) =>
                setProjectToEdit({
                  ...projectToEdit,
                  projectName: e.target.value,
                })
              }
            />
          </div>
          <div className="grid w-full items-center gap-1.5"></div>
          <Button onClick={onClickHandler}>
            {project ? "Update Project" : "Create Project"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
