"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { api } from "@/trpc/react";
import { toast } from "react-toastify";
import { UploadButton, UploadDropzone } from "@/components/uploadThings";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Company, PaymentMethod } from "@prisma/client";
import { useEffect, useMemo, useState } from "react";
import { redirect, useParams, useRouter } from "next/navigation";
import { ClientUploadedFileData } from "uploadthing/types";
import Image from "next/image";
import ImageViewer from "@/components/ui/ImageViewer";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import SelectCreate from "@/app/(app)/_components/select-create";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AttachmentUploader } from "@/app/_components/attachmentUploader";
type ComT = Partial<Company & { alternatives: string[] }>;

export default function Page() {
  const router = useRouter();
  const { companyId } = useParams<{ companyId: string }>();
  const {
    data: company,
    refetch,
    isLoading,
  } = api.company.getOne.useQuery({
    companyId,
  });
  const [attachments, setAttachments] = useState<
    ClientUploadedFileData<{ uploadedBy: string }>[]
  >([]);
  const [showMenu, setShowMenu] = useState<any>(undefined);
  const [companyToEdit, setCompanyToEdit] = useState<Partial<Company>>(
    company || {
      companyName: "",
      description: "",
      address: "",
      companyPhone: "",
      contactPerson: "",
      contactPhone: "",
      email: "",
      legalName: "",
      nationalId: "",
      salesTaxNumber: "",
      type: "Company",
      paymentMethod: null,
      iban: "",
      swiftCode: "",
      nameOnCheque: "",
      accountName: "",
      cliq: "",
      identifier: "",
    },
  );

  useEffect(() => {
    if (company) {
      setCompanyToEdit({ ...company } as ComT);
    }
  }, [company]);

  const { mutate: updateCompany } = api.company.update.useMutation();
  const { mutate: createCompany } = api.company.create.useMutation();
  const { data: categories } = api.company.getAllCategories.useQuery();

  const { mutate } = api.attachment.create.useMutation();
  const { mutate: deleteAttachment } = api.attachment.delete.useMutation();
  console.log(company);
  const { mutate: deleteAttachmentFromUT } =
    api.attachment.deleteFromUT.useMutation();
  const deleteAttachmentUTHandler = (keys: string[]) => {
    deleteAttachmentFromUT(
      {
        keys: keys,
      },
      {
        onSuccess: () => {
          setAttachments(
            attachments.filter((attachment) => !keys.includes(attachment.key)),
          );
        },
      },
    );
  };
  const createAttachments = async (
    attachments: ClientUploadedFileData<{
      uploadedBy: string;
    }>[],
    companyId: string,
  ) => {
    mutate(
      {
        companyId,
        attachments,
      } as any,
      {
        onSuccess: () => {
          setAttachments([]);
          refetch();
        },
      },
    );
  };

  const updateCompanyHandler = () => {
    if (!company) return;
    if (companyToEdit.companyName?.trim() === "")
      return toast.error("Company name is required");

    console.log("aaaaaaaa ", companyToEdit);

    updateCompany(
      {
        ...(companyToEdit as any),
      },
      {
        onSuccess: () => {
          toast.success("Company Updated Successfully");
          refetch();
        },
      },
    );
  };

  useEffect(() => {
    if (attachments?.length && company?.companyId) {
      createAttachments(attachments, company.companyId);
    }
  }, [attachments, company]);

  const createCompanyHandler = () => {
    if (companyToEdit.companyName?.trim() === "")
      return toast.error("Company name is required");
    createCompany(
      {
        ...(companyToEdit as any),
      },
      {
        onSuccess: (res) => {
          if (attachments?.length) {
            createAttachments(attachments, res.companyId);
          }
          router.push(`/manage/companies/${res.companyId}`);
        },
      },
    );
  };

  const deleteAttachmentHandler = ({
    attachmentId,
    key,
    companyAttachmentId,
  }: {
    attachmentId: string;
    key: string;
    companyAttachmentId: string;
  }) => {
    if (!company) return;
    deleteAttachment(
      {
        attachmentId: attachmentId,
        key: key,
        companyAttachmentId: companyAttachmentId,
      },
      {
        onSuccess: () => {
          refetch();
          toast.success("Attachment deleted successfully");
        },
      },
    );
  };

  const onClickHandler = () => {
    if (company) {
      updateCompanyHandler();
    } else {
      createCompanyHandler();
    }
  };

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between">
            {company ? "Update vendor info" : "Create new vendor"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid w-full grid-cols-2 gap-3">
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="companyName">Vendor Name</Label>
              <Input
                id="companyName"
                type="text"
                value={companyToEdit.companyName}
                onChange={(e) =>
                  setCompanyToEdit({
                    ...companyToEdit,
                    companyName: e.target.value,
                  })
                }
              />
            </div>
            <div>
              <Label>Vendor Type</Label>
              <Select
                value={companyToEdit?.type || undefined}
                onValueChange={(v: "Company" | "Individual") => {
                  setCompanyToEdit({ ...companyToEdit, type: v });
                }}
              >
                <SelectTrigger className="">
                  <SelectValue placeholder="Select vendor type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="Company">Company</SelectItem>
                    <SelectItem value="Individual">Individual</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            {companyToEdit?.type === "Company" && (
              <>
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="legalName">Legal Name</Label>
                  <Input
                    id="legalName"
                    type="text"
                    value={companyToEdit.legalName}
                    onChange={(e) =>
                      setCompanyToEdit({
                        ...companyToEdit,
                        legalName: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="companyPhone">Company Phone</Label>
                  <Input
                    id="companyPhone"
                    type="text"
                    value={companyToEdit.companyPhone}
                    onChange={(e) =>
                      setCompanyToEdit({
                        ...companyToEdit,
                        companyPhone: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    type="text"
                    value={companyToEdit.address}
                    onChange={(e) =>
                      setCompanyToEdit({
                        ...companyToEdit,
                        address: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="contactPerson">Contact Person</Label>
                  <Input
                    id="contactPerson"
                    type="text"
                    value={companyToEdit.contactPerson}
                    onChange={(e) =>
                      setCompanyToEdit({
                        ...companyToEdit,
                        contactPerson: e.target.value,
                      })
                    }
                  />
                </div>
              </>
            )}
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="contactPhone">Contact Phone</Label>
              <Input
                id="contactPhone"
                type="text"
                value={companyToEdit.contactPhone}
                onChange={(e) =>
                  setCompanyToEdit({
                    ...companyToEdit,
                    contactPhone: e.target.value,
                  })
                }
              />
            </div>
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="text"
                value={companyToEdit.email}
                onChange={(e) =>
                  setCompanyToEdit({
                    ...companyToEdit,
                    email: e.target.value,
                  })
                }
              />
            </div>
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="nationalId">National ID</Label>
              <Input
                id="nationalId"
                type="text"
                value={companyToEdit?.nationalId || ""}
                onChange={(e) =>
                  setCompanyToEdit({
                    ...companyToEdit,
                    nationalId: e.target.value,
                  })
                }
              />
            </div>
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="salesTaxNumber">Sales Tax Number</Label>
              <Input
                id="salesTaxNumber"
                type="text"
                value={companyToEdit?.salesTaxNumber || ""}
                onChange={(e) =>
                  setCompanyToEdit({
                    ...companyToEdit,
                    salesTaxNumber: e.target.value,
                  })
                }
              />
            </div>
            <div
              className="col-span-2 w-full gap-1.5"
              onClick={() => setShowMenu(undefined)}
            >
              <SelectCreate
                menuIsOpen={!companyToEdit?.description ? undefined : showMenu}
                noOptionsMessage={({ inputValue }) =>
                  `Press Enter to create new category "${inputValue}"`
                }
                // menuIsOpen={!companyToEdit.description}
                label="Select Category"
                placeholder="select category"
                value={{
                  value: companyToEdit.description || "",
                  label: companyToEdit.description || "",
                }}
                create={(v) => {
                  setShowMenu(false);

                  setCompanyToEdit({
                    ...companyToEdit,
                    description: v,
                  });
                }}
                onChange={(c) => {
                  setShowMenu(undefined);
                  setCompanyToEdit({
                    ...companyToEdit,
                    description: c?.value,
                  });
                }}
                options={
                  categories?.map((cat) => ({
                    value: cat.description || "",
                    label: cat.description || "",
                  })) || []
                }
              />
            </div>
          </div>
          <hr />
          <div className="my-3 grid grid-cols-1 gap-5 md:grid-cols-2">
            <div
              className={`${companyToEdit.paymentMethod ? "col-span-1" : "!col-span-2 w-full"}`}
            >
              <Label>Payment Method</Label>
              <Select
                value={companyToEdit.paymentMethod || undefined}
                onValueChange={(v: PaymentMethod) => {
                  setCompanyToEdit({ ...companyToEdit, paymentMethod: v });
                }}
              >
                <SelectTrigger className="">
                  <SelectValue placeholder="Select a Payment Method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Payment Method</SelectLabel>

                    <SelectItem value="bankTransfer">Bank Transfer</SelectItem>
                    <SelectItem value="cheque">Cheque</SelectItem>
                    <SelectItem value="CLIQ">CLIQ</SelectItem>
                    <SelectItem value="eFAWATEERCOM">eFAWATEERCOM</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            {companyToEdit.paymentMethod === "bankTransfer" ? (
              <>
                <div>
                  <Label htmlFor="iban">IBAN</Label>
                  <Input
                    onChange={(e) => {
                      setCompanyToEdit({
                        ...companyToEdit,
                        iban: e.target.value,
                      });
                    }}
                    value={companyToEdit.iban || ""}
                    id="iban"
                    type="text"
                  />
                </div>
                <div>
                  <Label htmlFor="swiftCode">Swift Code</Label>
                  <Input
                    onChange={(e) => {
                      setCompanyToEdit({
                        ...companyToEdit,
                        swiftCode: e.target.value,
                      });
                    }}
                    value={companyToEdit.swiftCode || ""}
                    id="swiftCode"
                    type="text"
                  />
                </div>
                <div>
                  <Label htmlFor="accountName">Account Name</Label>
                  <Input
                    onChange={(e) => {
                      setCompanyToEdit({
                        ...companyToEdit,
                        accountName: e.target.value,
                      });
                    }}
                    value={companyToEdit.accountName || ""}
                    id="accountName"
                    type="text"
                  />
                </div>
              </>
            ) : companyToEdit.paymentMethod === "CLIQ" ? (
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="cliq">
                  CLIQ <small> (Alias / number) </small>
                </Label>
                <Input
                  value={companyToEdit.cliq || ""}
                  onChange={(e) => {
                    setCompanyToEdit({
                      ...companyToEdit,
                      cliq: e.target.value.toUpperCase(),
                    });
                  }}
                  id="cliq"
                  type="text"
                />
              </div>
            ) : companyToEdit.paymentMethod === "cheque" ? (
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="nameOnCheque">Name On Cheque</Label>
                <Input
                  value={companyToEdit.nameOnCheque || ""}
                  onChange={(e) => {
                    setCompanyToEdit({
                      ...companyToEdit,
                      nameOnCheque: e.target.value,
                    });
                  }}
                  id="nameOnCheque"
                  type="text"
                />
              </div>
            ) : companyToEdit.paymentMethod === "eFAWATEERCOM" ? (
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="identifier">Identifier</Label>
                <Input
                  value={companyToEdit.identifier || ""}
                  onChange={(e) => {
                    setCompanyToEdit({
                      ...companyToEdit,
                      identifier: e.target.value,
                    });
                  }}
                  id="identifier"
                  type="text"
                />
              </div>
            ) : null}
          </div>
          <div className="grid w-full items-center gap-1.5">
            <div>
              <div className="flex flex-wrap gap-2">
                <AttachmentUploader
                  attachments={attachments}
                  setAttachments={setAttachments}
                />
                <div className="flex flex-wrap gap-5">
                  {company &&
                    company.CompanyAttachments.map((a) => {
                      return (
                        <div
                          key={a.attachmentId}
                          className="group relative flex items-center justify-center rounded-md border border-dotted"
                        >
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <span className="absolute right-0 top-0 hidden size-5 cursor-pointer items-center justify-center rounded-full bg-red-500 group-hover:flex">
                                <X size={10} className="text-white" />
                              </span>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Delete Attachment
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this
                                  attchment?
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => {
                                    deleteAttachmentHandler({
                                      attachmentId: a.attachmentId,
                                      key: a.Attachment.key || "",
                                      companyAttachmentId:
                                        a.companyAttachmentId,
                                    });
                                  }}
                                  className="bg-red-600"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                          <ImageViewer
                            // alt={a.Attachment.name}
                            w={100}
                            h={100}
                            attachment={a.Attachment}
                            // src={a.Attachment.url}
                            onDel={() => {}}
                            className="max-h-[100px] object-contain"
                          />
                        </div>
                      );
                    })}
                </div>
                <div className="flex flex-wrap gap-5">
                  {!company &&
                    attachments.map((a) => {
                      return (
                        <div
                          key={a.key}
                          className="group relative flex items-center justify-center rounded-md border border-dotted"
                        >
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <span className="absolute right-0 top-0 hidden size-5 cursor-pointer items-center justify-center rounded-full bg-red-500 group-hover:flex">
                                <X size={10} className="text-white" />
                              </span>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Delete Attachment
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this
                                  attchment?
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => {
                                    deleteAttachmentUTHandler([a.key]);
                                  }}
                                  className="bg-red-600"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                          <ImageViewer
                            // alt={a.Attachment.name}
                            w={100}
                            h={100}
                            attachment={a}
                            // src={a.Attachment.url}
                            onDel={() => {}}
                            className="max-h-[100px] object-contain"
                          />
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          </div>
          <Button onClick={onClickHandler}>
            {company ? "Update Vendor" : "Create Vendor"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
