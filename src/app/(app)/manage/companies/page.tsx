"use client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { useMemo, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { api } from "@/trpc/react";

export default function Page() {
  const { data: companies, refetch, isLoading } = api.company.getAll.useQuery();
  const router = useRouter();
  const { mutate: deleteCompany } = api.company.delete.useMutation();

  // State for search and pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Filter companies based on the search term
  const filteredCompanies = useMemo(() => {
    if (!companies) return [];
    const lowerSearch = searchTerm.toLowerCase();
    return companies.filter(
      (com) =>
        com.companyName.toLowerCase().includes(lowerSearch) ||
        (com.description &&
          com.description.toLowerCase().includes(lowerSearch)),
    );
  }, [companies, searchTerm]);

  // Calculate total pages (20 items per page)
  const totalPages = Math.ceil(filteredCompanies.length / 20);

  // Slice companies list for current page
  const paginatedCompanies = useMemo(() => {
    const startIndex = (currentPage - 1) * 20;
    return filteredCompanies.slice(startIndex, startIndex + 20);
  }, [filteredCompanies, currentPage]);

  return (
    <div className="space-y-5 p-5">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Business is growing?
            <Link href="/manage/companies/new" className="underline">
              <Button variant={"outline"}>Create new vendor</Button>
            </Link>
          </CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between">
          <CardTitle className="mb-2 md:mb-0">Manage Vendors</CardTitle>
          <Input
            type="text"
            placeholder="Search vendors"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); // Reset to page 1 when searching
            }}
            className="max-w-xs"
          />
        </CardHeader>
        <CardContent>
          <Table>
            <TableCaption>
              Showing {paginatedCompanies.length} of {filteredCompanies.length}{" "}
              companies
            </TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead>Vendor Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedCompanies.map((com) => (
                <TableRow key={com.companyId}>
                  <TableCell className="font-medium">
                    {com.companyName}
                  </TableCell>
                  <TableCell className="font-medium">
                    {com.description || "N/A"}
                  </TableCell>
                  <TableCell className="font-medium">
                    {com.type || "N/A"}
                  </TableCell>
                  <TableCell className="font-medium">
                    {com.createdAt.toDateString()}
                  </TableCell>
                  <TableCell className="font-medium">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(`/manage/companies/${com.companyId}`)
                          }
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            deleteCompany(
                              { companyId: com.companyId },
                              {
                                onSuccess: () => {
                                  refetch();
                                  toast.success("Company deleted successfully");
                                },
                              },
                            );
                          }}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            {(isLoading || !companies?.length) && (
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    {isLoading ? "Loading..." : "No companies found"}
                  </TableCell>
                </TableRow>
              </TableFooter>
            )}
          </Table>
          {/* Pagination Controls */}
          <div className="mt-4 flex items-center justify-between">
            <Button
              variant="outline"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => prev - 1)}
            >
              Previous
            </Button>
            <span>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant="outline"
              disabled={currentPage === totalPages || totalPages === 0}
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
