import StatsDashboard from "./_components/dashboard/statsDashboard";
import ToReviewOrders from "./_components/dashboard/to-review-orders";
import { Separator } from "@/components/ui/separator";
import { getServerAuthSession } from "@/server/auth";
import { api } from "@/trpc/server";

export default async function Page() {
  const session = await getServerAuthSession();
  const purchaseOrders = await api.dashboard.getPOsToReview();

  return (
    <div className="space-y-4 p-5">
      <ToReviewOrders purchaseOrders={(purchaseOrders || []) as any} />
      <Separator />
      {["admin", "ceo", "accountant", "accountantManager", "auditor"].includes(
        session?.user.role.role || "",
      ) ? (
        <StatsDashboard />
      ) : null}
    </div>
  );
}
