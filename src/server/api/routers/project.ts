import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";

export const projectRouter = createTRPCRouter({
  getAll: publicProcedure.query(({ ctx }) => {
    return ctx.db.project.findMany({
      where: {
        closed: {
          equals: false,
        },
      },
    });
  }),
  getOne: publicProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.project.findUnique({
        where: {
          projectId: input.projectId,
        },
      });
    }),
  getDashboard: publicProcedure.query(({ ctx }) => {
    return ctx.db.project.findMany({
      orderBy: {
        createdAt: "desc",
      },
      select: {
        closed: true,
        projectName: true,
        projectId: true,
        createdAt: true,
        updatedAt: true,
        _count: true,
      },
    });
  }),
  create: protectedProcedure
    .input(z.object({ projectName: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.project.create({
        data: {
          projectName: input.projectName,
        },
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        projectName: z.string().min(1),
        projectId: z.string().min(1),
        closed: z.boolean(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.project.update({
        where: {
          projectId: input.projectId,
        },
        data: {
          projectName: input.projectName,
          closed: input.closed,
        },
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        projectId: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.project.delete({
        where: {
          projectId: input.projectId,
        },
      });
    }),

  tstUser: protectedProcedure.query(async ({ ctx }) => {
    console.log("asd", ctx.session.user);
    return ctx.session.user;
  }),
});
