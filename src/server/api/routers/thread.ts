import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";

export const threadRouter = createTRPCRouter({
  getPoThread: publicProcedure
    .input(z.object({ purchaseOrderId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.purchaseOrderThread.findMany({
        include: {
          user: true,
        },
        where: { purchaseOrderId: input.purchaseOrderId },
      });
    }),

  sendMessage: protectedProcedure
    .input(
      z.object({
        purchaseOrderId: z.string(),
        message: z.string(),
      }),
    )
    .mutation(({ ctx, input }) => {
      return ctx.db.purchaseOrderThread.create({
        data: {
          purchaseOrderId: input.purchaseOrderId,
          message: input.message,
          id: ctx.session.user.id,
        },
      });
    }),
});
