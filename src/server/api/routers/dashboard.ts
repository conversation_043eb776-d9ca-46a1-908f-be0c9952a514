import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { PurchaseOrderI } from "@/lib/types";
import { Currency, PaymentStatus } from "@prisma/client";
const convertFromCurrencyTOJod = (curr: Currency, amount: number) => {
  if (curr === "JOD") {
    return amount;
  }
  if (curr === "USD") {
    return amount * 0.71;
  }
  if (curr === "EUR") {
    return amount * 0.81;
  }
  if (curr === "GBP") {
    return amount * 0.95;
  }
  if (curr === "AED") {
    return amount * 0.19;
  }
  return 0;
};
export const dashboardRouter = createTRPCRouter({
  getProjectsAndTotal: protectedProcedure.query(async ({ ctx, input }) => {
    const pod = await ctx.db.project.findMany({
      where: {
        PurchaseOrderDetails: {
          some: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), 0, 1),
              lte: new Date(new Date().getFullYear(), 11, 31),
            },
          },
        },
      },
      include: {
        PurchaseOrderDetails: {
          include: {
            PurchaseOrder: true,
            PurchaseOrderPayments: true,
          },
        },
      },
    });

    const totalPerProject = pod.map((p) => {
      const total = p.PurchaseOrderDetails.reduce(
        (acc, curr) =>
          // acc + (curr.totalAmount || 0),

          acc +
          convertFromCurrencyTOJod(
            curr.currency,
            curr.PurchaseOrder.paymentType == "cash"
              ? curr.PurchaseOrder.isPaid
                ? curr.totalAmount || 0
                : 0
              : curr.PurchaseOrderPayments.reduce(
                  (acc, curr) => acc + (curr.paidAt ? curr.amount : 0),
                  0,
                ),
          ),
        0,
      );
      return {
        projectName: p.projectName,
        totalAmount: total,
      };
    });

    return {
      companyCount: await ctx.db.company.count(),
      projectCount: await ctx.db.project.count(),
      totalPerProject,
      projects: pod,
      totalPurchaseOrders: await ctx.db.purchaseOrder.count({
        where: {
          deletedAt: null,
          isPaid: true,
        },
      }),
    };
  }),

  getPOsToReview: protectedProcedure.query(async ({ ctx }) => {
    const userMatchStatus = (
      status: PaymentStatus,
      purchaseOrder: Partial<PurchaseOrderI>,
    ) => {
      if (
        status == "toReview" &&
        ctx.session.user.id === purchaseOrder.userReviewId
      ) {
        return {
          action: "review",
        };
      } else if (
        status == "toApprove" &&
        ctx.session.user.id === purchaseOrder.userApproveId
      ) {
        return {
          action: "approve",
        };
      } else if (
        status == "toAudit" &&
        (ctx.session.user.role.role === "auditor" ||
          ctx.session.user.id === purchaseOrder.userAuditId)
      ) {
        return {
          action: "audit",
        };
      } else if (
        status == "toPay" &&
        ctx.session.user.role.role === "accountantManager"
      ) {
        return {
          action: "transfer-pay",
        };
      } else if (
        status == "transferred" &&
        ctx.session.user.role.role.includes("accountant")
      ) {
        return {
          action: "pay",
        };
      } else {
        return null;
      }
    };
    const allPos = await ctx.db.purchaseOrder.findMany({
      where: {
        deletedAt: null,
      },
      include: {
        userPrepare: true,
        PurchaseOrderDetails: {
          include: {
            project: true,
            company: true,
            PurchaseOrderPayments: true,
          },
        },
      },
    });

    const final = allPos.filter((po) =>
      po.PurchaseOrderDetails?.PurchaseOrderPayments.some((p) =>
        userMatchStatus(p.status, po as any),
      ),
    );

    return final;
  }),

  getPOs: protectedProcedure
    .input(
      z.object({
        filter: z
          .object({
            isPaid: z.boolean().optional(),
            search: z.string().optional(),
            dateRange: z
              .object({
                from: z.date().optional(),
                to: z.date().optional(),
              })
              .optional(),
            projectId: z.array(z.string()).optional(),
            companyId: z.array(z.string()).optional(),
            paymentType: z.array(z.string()).optional(),
            paymentMethod: z.array(z.string()).optional(),
          })
          .default({}),
        pagination: z
          .object({
            skip: z.number().min(0).default(0),
            take: z.number().min(1).default(10),
          })
          .default({}),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const where: any = {
        deletedAt: null,
        isDraft: false,
      };

      if (input.filter.search) {
        const isNumberSearch = !isNaN(Number(input.filter.search));

        where.OR = [
          {
            purchaseOrderId: {
              contains: input.filter.search,
              mode: "insensitive",
            },
          },
          {
            PurchaseOrderDetails: {
              description: {
                contains: input.filter.search,
                mode: "insensitive",
              },
            },
          },
          {
            PurchaseOrderDetails: {
              project: {
                projectName: {
                  contains: input.filter.search,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            PurchaseOrderDetails: {
              company: {
                companyName: {
                  contains: input.filter.search,
                  mode: "insensitive",
                },
              },
            },
          },
          ...(isNumberSearch
            ? [
                {
                  referenceNumber: Number(input.filter.search),
                },
              ]
            : []),
        ];
      }

      if (input.filter.dateRange) {
        const { from, to } = input.filter.dateRange;
        if (from || to) {
          where.createdAt = {};
          if (from) {
            where.createdAt.gte = from;
          }
          if (to) {
            where.createdAt.lte = to;
          }
        }
      }

      if (input.filter.projectId) {
        where.PurchaseOrderDetails = {
          ...where.PurchaseOrderDetails,
          projectId: { in: input.filter.projectId },
        };
      }

      if (input.filter.companyId) {
        where.PurchaseOrderDetails = {
          ...where.PurchaseOrderDetails,
          companyId: { in: input.filter.companyId },
        };
      }

      if (input.filter.paymentType) {
        where.paymentType = { in: input.filter.paymentType };
      }

      if (input.filter.paymentMethod) {
        where.PurchaseOrderDetails = {
          ...where.PurchaseOrderDetails,
          paymentMethod: { in: input.filter.paymentMethod },
        };
      }

      if (typeof input.filter.isPaid !== "undefined") {
        where.isPaid = input.filter.isPaid;
      }

      // Use the sort input; defaults are already set.

      const { skip, take } = input.pagination;

      // Query the purchase orders along with all related data.
      const data = await ctx.db.purchaseOrder.findMany({
        where,
        include: {
          PurchaseOrderDetails: {
            include: {
              PurchaseOrderPayments: {
                include: {
                  paidBy: true,
                },
              },
              company: true,
              project: true,
            },
          },
          purchaseOrderAttachments: true,
          userPrepare: true,
          userReview: true,
          userApprove: true,
          userAudit: true,
        },
        skip,
        take,
      });
      console.log("data", data);
      // Count total matching records for pagination.
      const total = await ctx.db.purchaseOrder.count({ where });

      return { data, total };
    }),
});
