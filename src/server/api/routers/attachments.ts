import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { ClientUploadedFileData } from "uploadthing/types";
import { UTApi } from "uploadthing/server";

export const attachmentRouter = createTRPCRouter({
  create: protectedProcedure
    .input(
      z.object({
        companyId: z.string().optional(),
        purchaseOrderId: z.string().optional(),
        PurchaseOrderInstallmentId: z.string().optional(),
        attachments: z.array(
          z.object({
            url: z.string(),
            type: z.string(),
            name: z.string(),
            key: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const {
        companyId,
        purchaseOrderId,
        PurchaseOrderInstallmentId,
        attachments,
      } = input;

      const createdAttachments = await ctx.db.attachment.createManyAndReturn({
        data: attachments.map((a) => {
          return {
            url: a.url,
            name: a.name,
            type: a.type,
            key: a.key,
          };
        }),
      });

      if (companyId) {
        await ctx.db.companyAttachment.createMany({
          data: createdAttachments.map((a) => {
            return {
              attachmentId: a.attachmentId,
              companyId: companyId,
            };
          }),
        });
        return ctx.db.companyAttachment.findMany({
          where: {
            companyId,
          },
          include: {
            Attachment: true,
          },
        });
      }

      if (purchaseOrderId) {
        await ctx.db.purchaseOrderAttachment.createMany({
          data: createdAttachments.map((a) => {
            return {
              attachmentId: a.attachmentId,
              purchaseOrderId: purchaseOrderId,
            };
          }),
        });
        return ctx.db.purchaseOrderAttachment.findMany({
          where: {
            purchaseOrderId,
          },
          include: {
            Attachment: true,
          },
        });
      }
    }),

  delete: protectedProcedure
    .input(
      z.object({
        attachmentId: z.string(),
        key: z.string(),
        companyAttachmentId: z.string().optional(),
        purchaseOrderAttachmentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { companyAttachmentId, purchaseOrderAttachmentId, key } = input;

      if (companyAttachmentId) {
        await ctx.db.companyAttachment.delete({
          where: {
            companyAttachmentId: companyAttachmentId,
          },
        });
      }

      if (purchaseOrderAttachmentId) {
        await ctx.db.purchaseOrderAttachment.delete({
          where: {
            purchaseOrderAttachmentId: purchaseOrderAttachmentId,
          },
        });
      }

      await ctx.db.attachment.delete({
        where: {
          attachmentId: input.attachmentId,
        },
      });

      // delet attachment from upload things
      const utapi = new UTApi();

      await utapi.deleteFiles(key);
    }),

  deleteFromUT: protectedProcedure
    .input(
      z.object({
        keys: z.array(z.string()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { keys } = input;
      const utapi = new UTApi();
      await utapi.deleteFiles(keys);
    }),
});
