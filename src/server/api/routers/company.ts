import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";

export const companyRouter = createTRPCRouter({
  getAll: publicProcedure.query(({ ctx }) => {
    return ctx.db.company.findMany();
  }),

  getOne: publicProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.db.company.findUnique({
        where: {
          companyId: input.companyId,
        },
        include: {
          CompanyAttachments: {
            include: {
              Attachment: true,
            },
          },
        },
      });
    }),

  create: protectedProcedure
    .input(
      z.object({
        companyName: z.string().min(1),
        description: z.string().optional(),
        legalName: z.string().optional(),
        companyPhone: z.string().optional(),
        email: z.string().optional(),
        salesTaxNumber: z.string().default("").nullable(),
        nationalId: z.string().optional(),
        address: z.string().optional(),
        contactPerson: z.string().optional(),
        contactPhone: z.string().optional(),
        type: z.string().optional(),
        paymentMethod: z.string().optional(),
        iban: z.string().optional(),
        swiftCode: z.string().optional(),
        nameOnCheque: z.string().optional(),
        accountName: z.string().optional(),
        cliq: z.string().optional(),
        identifier: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.company.create({
        data: {
          companyName: input.companyName,
          description: input.description,
          legalName: input.legalName,
          companyPhone: input.companyPhone,
          email: input.email,
          salesTaxNumber: input.salesTaxNumber,
          nationalId: input.nationalId,
          address: input.address,
          contactPerson: input.contactPerson,
          contactPhone: input.contactPhone,
          type: input.type as any,
          paymentMethod: input.paymentMethod as any,
          iban: input.iban,
          swiftCode: input.swiftCode,
          nameOnCheque: input.nameOnCheque,
          accountName: input.accountName,
          cliq: input.cliq,
          identifier: input.identifier,
        },
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        companyId: z.string().min(1),
        companyName: z.string().min(1),
        description: z.string().optional(),
        legalName: z.string().optional(),
        companyPhone: z.string().optional(),
        email: z.string().optional(),
        salesTaxNumber: z.string().default("").nullable(),
        nationalId: z.string().optional(),
        address: z.string().optional(),
        contactPerson: z.string().optional(),
        contactPhone: z.string().optional(),
        type: z.string().optional(),
        paymentMethod: z.string().optional(),
        iban: z.string().nullable(),
        swiftCode: z.string().nullable(),
        nameOnCheque: z.string().nullable(),
        accountName: z.string().nullable(),
        cliq: z.string().nullable(),
        identifier: z.string().nullable(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.company.update({
        where: {
          companyId: input.companyId,
        },
        data: {
          companyName: input.companyName,
          description: input.description,
          legalName: input.legalName,
          companyPhone: input.companyPhone,
          email: input.email,
          salesTaxNumber: input.salesTaxNumber,
          nationalId: input.nationalId,
          address: input.address,
          contactPerson: input.contactPerson,
          contactPhone: input.contactPhone,
          type: input.type as any,
          paymentMethod: input.paymentMethod as any,
          iban: input.iban,
          swiftCode: input.swiftCode,
          nameOnCheque: input.nameOnCheque,
          accountName: input.accountName,
          cliq: input.cliq,
          identifier: input.identifier,
        },
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        companyId: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.db.company.delete({
        where: {
          companyId: input.companyId,
        },
      });
    }),

  tstUser: protectedProcedure.query(async ({ ctx }) => {
    console.log("asd", ctx.session.user);
    return ctx.session.user;
  }),
  getAllCategories: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.company.findMany({
      select: {
        description: true,
      },
      distinct: ["description"],
    });
  }),
});
