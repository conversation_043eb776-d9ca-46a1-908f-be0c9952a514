import {
  inviteSchema,
  registerSchema,
  updateUserSchema,
} from "@/validation/auth";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcrypt";
import { publicProcedure, createTRPCRouter, protectedProcedure } from "../trpc";
import { z } from "zod";
import { transporter } from "@/server/common/email.service";
import { emailTemplate } from "@/lib/utils";
import { env } from "process";
const SALT_ROUNDS = 10;

export const authRouter = createTRPCRouter({
  getAll: publicProcedure.query(({ ctx }) => {
    return ctx.db.user.findMany({
      select: {
        email: true,
        id: true,
        username: true,
        password: true,
        role: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });
  }),
  getUserToJoin: publicProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(({ input, ctx }) => {
      return ctx.db.user.findUnique({
        where: {
          id: input.id,
          // password: null,
        },
      });
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { id } = input;
      const result = await ctx.db.user.delete({
        where: {
          id,
        },
      });
      return result;
    }),
  getByEmail: publicProcedure
    .input(z.object({ email: z.string() }))
    .mutation(({ input, ctx }) => {
      return ctx.db.user.findUnique({
        where: {
          email: input.email,
        },
      });
    }),
  update: protectedProcedure
    .input(updateUserSchema)
    .mutation(async ({ input, ctx }) => {
      const { username, roleId, id } = input;
      const result = await ctx.db.user.update({
        where: {
          id,
        },
        data: {
          username,
          roleId,
        },
      });
      return result;
    }),
  inviteRegister: publicProcedure
    .input(
      z.object({ id: z.string(), username: z.string(), password: z.string() }),
    )
    .mutation(async ({ input, ctx }) => {
      const { username, password, id } = input;
      const salt = bcrypt.genSaltSync(SALT_ROUNDS);
      const hash = bcrypt.hashSync(password, salt);
      const result = await ctx.db.user.update({
        where: {
          id,
        },
        data: {
          username,
          password: hash,
        },
      });
      return result;
    }),
  register: publicProcedure
    .input(registerSchema)
    .mutation(async ({ input, ctx }) => {
      const { username, email, password, roleId } = input;

      const exists = await ctx.db.user.findFirst({
        where: { email },
      });

      if (exists) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User already exists.",
        });
      }

      const salt = bcrypt.genSaltSync(SALT_ROUNDS);
      const hash = bcrypt.hashSync(password, salt);

      const result = await ctx.db.user.create({
        data: {
          username,
          email,
          password: hash,
          roleId: roleId,
        },
      });
      console.log(result);

      return {
        status: 201,
        message: "Account created successfully",
        result: result.email,
      };
    }),
  invite: publicProcedure
    .input(inviteSchema)
    .mutation(async ({ input, ctx }) => {
      const { email, roleId } = input;

      const exists = await ctx.db.user.findFirst({
        where: { email },
      });

      if (exists) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User already exists.",
        });
      }

      const result = await ctx.db.user.create({
        data: {
          // username,
          email,
          roleId: roleId,
          // password: hash,
        },
      });

      transporter.sendMail({
        from: env.EMAIL_USER,
        to: email,
        subject: "Welcome to 16th of May PO system",
        html: emailTemplate({
          buttonText: "Join Now",
          toName: email || "",
          title: "Invitation to join 16th of May PO system",
          url: "https://po.16thofmay.com/join/" + result.id,
          message:
            "You have been invited to join 16th of May PO system By the admin",
        }),
      });
      console.log(result);

      return {
        status: 201,
        message: "Account created successfully",
        result: result,
      };
    }),
});
