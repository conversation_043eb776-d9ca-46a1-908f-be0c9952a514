import {
  Attachment,
  Company,
  Notification,
  Project,
  PurchaseOrder,
  PurchaseOrderAttachment,
  PurchaseOrderDetails,
  PurchaseOrderItem,
  PurchaseOrderPayment,
  PurchaseOrderThread,
  Role,
  User,
} from "@prisma/client";

export interface UserI extends User {
  role: Role;
}

export interface PurchaseOrderDetailsType extends PurchaseOrderDetails {
  PurchaseOrderItems: PurchaseOrderItem[];
  PurchaseOrderPayments: (PurchaseOrderPayment & { paidBy: User })[];
  company: Company;
  project: Project;
}

export interface PurchaseOrderAttachmentType extends PurchaseOrderAttachment {
  Attachment: Attachment;
}
export interface PurchaseOrderThreadType extends PurchaseOrderThread {
  user: User;
}

export interface PurchaseOrderI extends PurchaseOrder {
  PurchaseOrderDetails: PurchaseOrderDetailsType;
  PurchaseOrderThreads: PurchaseOrderThreadType[];
  userPrepare: User;
  userReview: User;
  userApprove: User;
  userAudit: User;
  Notifications: Notification[];
  purchaseOrderAttachments: PurchaseOrderAttachmentType[];
}
