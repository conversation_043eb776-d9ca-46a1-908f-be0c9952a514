import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const emailTemplate = (data: {
  toName: string;
  title: string;
  url: string;
  message: string;
  buttonText?: string;
}) => {
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="en">
  <head>
    <link
      rel="preload"
      as="image"
      href="https://uvanm13hib.ufs.sh/f/1ESEJW7fkdygOix1GNYAqX2HksIEf9MJLBFo7deaWcrRyOKw"
    />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
    <!--$-->
  </head>

  <body
    style="
      background-color: rgb(255, 255, 255);
      margin-top: auto;
      margin-bottom: auto;
      margin-left: auto;
      margin-right: auto;
      font-family: ui-sans-serif, system-ui, sans-serif,
        &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;,
        &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    "
  >
    <table
      align="center"
      width="100%"
      border="0"
      cellpadding="0"
      cellspacing="0"
      role="presentation"
      style="
        border-width: 1px;
        border-style: solid;
        border-color: rgb(234, 234, 234);
        border-radius: 0.25rem;
        margin-top: 40px;
        margin-bottom: 40px;
        margin-left: auto;
        margin-right: auto;
        padding: 20px;
        max-width: 465px;
      "
    >
      <tbody>
        <tr style="width: 100%">
          <td>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-top: 32px"
            >
              <tbody>
                <tr>
                  <td>
                    <img
                      alt="Vercel"
                      height="70"
                      src="https://uvanm13hib.ufs.sh/f/1ESEJW7fkdygOix1GNYAqX2HksIEf9MJLBFo7deaWcrRyOKw"
                      style="
                        margin-top: 0px;
                        margin-bottom: 0px;
                        margin-left: auto;
                        margin-right: auto;
                        display: block;
                        outline: none;
                        border: none;
                        text-decoration: none;
                      "
                      width="70"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
            <h1
              style="
                color: rgb(0, 0, 0);
                font-size: 24px;
                font-weight: 400;
                text-align: center;
                padding: 0px;
                margin-top: 30px;
                margin-bottom: 30px;
                margin-left: 0px;
                margin-right: 0px;
              "
            >
              ${data.title}
            </h1>
            <p
              style="
                color: rgb(0, 0, 0);
                font-size: 14px;
                line-height: 24px;
                margin: 16px 0;
              "
            >
              Hello ${data.toName},
            </p>
            <p
              style="
                color: rgb(0, 0, 0);
                font-size: 14px;
                line-height: 24px;
                margin: 16px 0;
              "
            >
              ${data.message}
            </p>

            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="text-align: center; margin-top: 32px; margin-bottom: 32px"
            >
              <tbody>
                <tr>
                  <td>
                    <a
                      href="${data.url}"
                      style="
                        background-color: rgb(0, 0, 0);
                        border-radius: 0.25rem;
                        color: rgb(255, 255, 255);
                        font-size: 12px;
                        font-weight: 600;
                        text-decoration-line: none;
                        text-align: center;
                        padding-left: 1.25rem;
                        padding-right: 1.25rem;
                        padding-top: 0.75rem;
                        padding-bottom: 0.75rem;
                        line-height: 100%;
                        text-decoration: none;
                        display: inline-block;
                        max-width: 100%;
                        mso-padding-alt: 0px;
                        padding: 12px 20px 12px 20px;
                      "
                      target="_blank"
                      ><span
                        ><!--[if mso
                          ]><i
                            style="mso-font-width: 500%; mso-text-raise: 18"
                            hidden
                            >&#8202;&#8202;</i
                          ><!
                        [endif]--></span
                      ><span
                        style="
                          max-width: 100%;
                          display: inline-block;
                          line-height: 120%;
                          mso-padding-alt: 0px;
                          mso-text-raise: 9px;
                        "
                        >${data.buttonText || "View Purchase Prder"}</span
                      ><span
                        ><!--[if mso
                          ]><i style="mso-font-width: 500%" hidden
                            >&#8202;&#8202;&#8203;</i
                          ><!
                        [endif]--></span
                      ></a
                    >
                  </td>
                </tr>
              </tbody>
            </table>
            <p
              style="
                color: rgb(0, 0, 0);
                font-size: 14px;
                line-height: 24px;
                margin: 16px 0;
              "
            >
              or copy and paste this URL into your browser:<!-- -->
              <a
                href="${data.url}"
                style="color: rgb(37, 99, 235); text-decoration-line: none"
                target="_blank"
                >${data.url}</a
              >
            </p>
            <hr
              style="
                border-width: 1px;
                border-style: solid;
                border-color: rgb(234, 234, 234);
                margin-top: 26px;
                margin-bottom: 26px;
                margin-left: 0px;
                margin-right: 0px;
                width: 100%;
                border: none;
                border-top: 1px solid #eaeaea;
              "
            />
            <p
              style="
                color: rgb(102, 102, 102);
                font-size: 12px;
                line-height: 24px;
                margin: 16px 0;
              "
            >
              This email was intended for<!-- -->
              <span style="color: rgb(0, 0, 0)">${data.toName}</span>. This
              email was sent on behalf of 16thofmay . If you were not expecting
              this email, you can ignore it.
            </p>
          </td>
        </tr>
      </tbody>
    </table>
    <!--/$-->
  </body>
</html>
`;
};

// idle
// toReview
// toApprove
// toAudit
// toPay
// transferred
// rejected
// paid
export const statusNameConverter = (status: string) => {
  switch (status) {
    case "idle":
      return { name: "Idle", color: "gray" };
    case "toReview":
      return { name: "To Review", color: "blue" };
    case "toApprove":
      return { name: "To Approve", color: "blue" };
    case "toAudit":
      return { name: "To Audit", color: "blue" };
    case "toPay":
      return { name: "To Pay", color: "blue" };
    case "transferred":
      return { name: "To Pay", color: "blue" };
    case "rejected":
      return { name: "Rejected", color: "red" };
    case "paid":
      return { name: "Paid", color: "green" };
    default:
      return { name: "Unknown", color: "gray" };
  }
};
