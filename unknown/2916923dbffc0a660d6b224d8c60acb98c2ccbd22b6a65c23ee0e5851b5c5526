[
  {
    purchaseOrderId: "cm3udrdi80071yn6umokywupm",
    status: "toReview",
    paid: false,
    userPrepareId: "cm3rj67wv0003lwm0fgfqtagu",
    userReviewId: "cm3rj9ynp000blwm0mujf46qu",
    userApproveId: "cm3rj8vh70005lwm0axjghiyd",
    createdAt: new Date("2024-11-23T16:24:14.000Z"),
    updatedAt: new Date("2024-11-23T16:44:17.000Z"),
    PurchaseOrderDetails: {
      purchaseOrderDetailId: "cm3udrdih0073yn6ukgnscbb6",
      description: null,
      createdAt: new Date("2024-11-23T16:24:14.000Z"),
      installment: false,
      projectId: "cm3rs69b90000134ekp3fvqgc",
      companyId: "cm3rjh23s0001z4yqz84qfrk6",
      paymentMethod: "bankTransfer",
      nameOnCheque: null,
      iban: null,
      cliq: null,
      date: new Date("2024-11-23T16:23:39.000Z"),
      contactName: null,
      contactNumber: null,
      currency: "JOD",
      totalAmount: 0,
      purchaseOrderId: "cm3udrdi80071yn6umokywupm",
      updatedAt: new Date("2024-11-23T16:44:17.000Z"),
      company: {
        companyId: "cm3rjh23s0001z4yqz84qfrk6",
        companyName: "company 1",
        createdAt: new Date("2024-11-21T16:40:52.000Z"),
        updatedAt: new Date("2024-11-21T16:40:52.000Z"),
      },
      project: {
        projectId: "cm3rs69b90000134ekp3fvqgc",
        projectName: "project 1",
        closed: false,
        createdAt: new Date("2024-11-21T20:44:25.000Z"),
        updatedAt: new Date("2024-11-21T20:44:25.000Z"),
      },
      PurchaseOrderItems: [
        {
          purchaseOrderItemId: "cm3udrdio0074yn6uura2y46s",
          description: "",
          priceNoTax: 50,
          priceTax: 50,
          taxAmount: 0,
          purchaseOrderDetailId: "cm3udrdih0073yn6ukgnscbb6",
          createdAt: new Date("2024-11-23T16:24:14.000Z"),
          updatedAt: new Date("2024-11-23T16:44:17.000Z"),
        },
      ],
    },
  },
  {
    purchaseOrderId: "cm3t8fct4000vyn6ur5dry3cx",
    status: "draft",
    paid: false,
    userPrepareId: "cm3rj67wv0003lwm0fgfqtagu",
    userReviewId: "cm3rj9ynp000blwm0mujf46qu",
    userApproveId: "cm3rj8vh70005lwm0axjghiyd",
    createdAt: new Date("2024-11-22T21:07:09.000Z"),
    updatedAt: new Date("2024-11-23T16:16:10.000Z"),
    PurchaseOrderDetails: {
      purchaseOrderDetailId: "cm3t8fctd000xyn6uicocdjkj",
      description: "dasdas",
      createdAt: new Date("2024-11-22T21:07:09.000Z"),
      installment: true,
      projectId: "cm3rs69b90000134ekp3fvqgc",
      companyId: "cm3rjh23s0001z4yqz84qfrk6",
      paymentMethod: "bankTransfer",
      nameOnCheque: null,
      iban: "asdas",
      cliq: null,
      date: new Date("2024-11-23T15:58:24.000Z"),
      contactName: "dasd",
      contactNumber: "********",
      currency: "JOD",
      totalAmount: 0,
      purchaseOrderId: "cm3t8fct4000vyn6ur5dry3cx",
      updatedAt: new Date("2024-11-23T16:16:10.000Z"),
      company: {
        companyId: "cm3rjh23s0001z4yqz84qfrk6",
        companyName: "company 1",
        createdAt: new Date("2024-11-21T16:40:52.000Z"),
        updatedAt: new Date("2024-11-21T16:40:52.000Z"),
      },
      project: {
        projectId: "cm3rs69b90000134ekp3fvqgc",
        projectName: "project 1",
        closed: false,
        createdAt: new Date("2024-11-21T20:44:25.000Z"),
        updatedAt: new Date("2024-11-21T20:44:25.000Z"),
      },
      PurchaseOrderItems: [
        {
          purchaseOrderItemId: "cm3ucxvj8005fyn6ugzzh2fqh",
          description: "c",
          priceNoTax: 50,
          priceTax: 50,
          taxAmount: 0,
          purchaseOrderDetailId: "cm3t8fctd000xyn6uicocdjkj",
          createdAt: new Date("2024-11-23T16:01:18.000Z"),
          updatedAt: new Date("2024-11-23T16:16:10.000Z"),
        },
      ],
    },
  },
  {
    purchaseOrderId: "cm3rv3q6s000be7ne8fgp4jrq",
    status: "draft",
    paid: false,
    userPrepareId: "cm3rj67wv0003lwm0fgfqtagu",
    userReviewId: "cm3rj9ynp000blwm0mujf46qu",
    userApproveId: "cm3rj8vh70005lwm0axjghiyd",
    createdAt: new Date("2024-11-21T22:06:25.000Z"),
    updatedAt: new Date("2024-11-22T21:05:37.000Z"),
    PurchaseOrderDetails: {
      purchaseOrderDetailId: "cm3rv3q72000de7ned6b87eax",
      description:
        '"\n' +
        "Invalid `prisma.purchaseOrderItem.update()` invocation:\n" +
        "\n" +
        "\n" +
        'An operation failed because it depends on one or more records that were required but not found. Record to update not found."',
      createdAt: new Date("2024-11-21T22:06:25.000Z"),
      installment: true,
      projectId: "cm3rs69b90000134ekp3fvqgc",
      companyId: "cm3rjh23s0001z4yqz84qfrk6",
      paymentMethod: "bankTransfer",
      nameOnCheque: null,
      iban: "adsasdasd",
      cliq: null,
      date: null,
      contactName: "asdas",
      contactNumber: "341412",
      currency: "JOD",
      totalAmount: 0,
      purchaseOrderId: "cm3rv3q6s000be7ne8fgp4jrq",
      updatedAt: new Date("2024-11-22T21:05:37.000Z"),
      company: {
        companyId: "cm3rjh23s0001z4yqz84qfrk6",
        companyName: "company 1",
        createdAt: new Date("2024-11-21T16:40:52.000Z"),
        updatedAt: new Date("2024-11-21T16:40:52.000Z"),
      },
      project: {
        projectId: "cm3rs69b90000134ekp3fvqgc",
        projectName: "project 1",
        closed: false,
        createdAt: new Date("2024-11-21T20:44:25.000Z"),
        updatedAt: new Date("2024-11-21T20:44:25.000Z"),
      },
      PurchaseOrderItems: [
        {
          purchaseOrderItemId: "cm3t8ddwq000syn6uwssohc2s",
          description: "aaa",
          priceNoTax: 10,
          priceTax: 10,
          taxAmount: 0,
          purchaseOrderDetailId: "cm3rv3q72000de7ned6b87eax",
          createdAt: new Date("2024-11-22T21:05:37.000Z"),
          updatedAt: new Date("2024-11-22T21:05:37.000Z"),
        },
        {
          purchaseOrderItemId: "cm3t8ddwq000tyn6u36y7d4yp",
          description: "bbb",
          priceNoTax: 10,
          priceTax: 10,
          taxAmount: 0,
          purchaseOrderDetailId: "cm3rv3q72000de7ned6b87eax",
          createdAt: new Date("2024-11-22T21:05:37.000Z"),
          updatedAt: new Date("2024-11-22T21:05:37.000Z"),
        },
      ],
    },
  },
];
