"use client";
import { Badge } from "@/components/ui/badge";
// import { db } from "@/server/db";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Bar, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, Cell } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import React, { useMemo } from "react";
import { PurchaseOrder } from "@prisma/client";
import {
  Banknote,
  Building,
  ChartNoAxesGantt,
  ReceiptText,
  Target,
} from "lucide-react";
import Link from "next/link";
import { api } from "@/trpc/react";
import { useRouter } from "next/navigation";

const StatsDashboard = () => {
  const { data } = api.dashboard.getProjectsAndTotal.useQuery();
  const router = useRouter();
  const chartConfig = {
    total: {
      label: "Total",
    },
  } satisfies ChartConfig;

  // Map API data to chart data.
  const chartData = useMemo(
    () =>
      data?.totalPerProject.map((i) => {
        return { project: i.projectName, total: i.totalAmount };
      }) || [],
    [data],
  );

  // Define an array of hex colors.
  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#413ea0"];

  return (
    <div className="gap-5">
      <Card>
        <CardHeader>
          <CardTitle>Total Spendings Per Project <small className="text-xs text-gray-400">in JOD</small></CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-52 w-full">
            <BarChart data={chartData}>
              <CartesianGrid vertical={false} />
              <XAxis dataKey="project" tickLine={false} axisLine={false} />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent hideLabel />}
              />
              <Bar dataKey="total" radius={8}>
                {chartData.map((entry, index) => (
                  <Cell
                  className="cursor-pointer"
                    onClick={(ds) => {
                      const project = data?.projects.find(
                        (p) => p.projectName === entry.project,
                      );
                      if (project) {
                        router.push(
                          `/dashboard?projectId=${project.projectId}&page=0&pageSize=10`,
                        );
                      }
                    }}
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Bar>
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default StatsDashboard;
