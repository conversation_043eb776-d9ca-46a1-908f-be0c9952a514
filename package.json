{"name": "po-system", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "start": "next start", "seed": "bun prisma/seed.ts"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.14.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.3", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@tanstack/react-table": "^8.20.5", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@uploadthing/react": "^7.1.5", "ably": "^2.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "framer-motion": "^11.7.0", "geist": "^1.3.0", "lucide-react": "^0.446.0", "mini-svg-data-uri": "^1.4.4", "next": "^14.2.4", "next-auth": "^4.24.7", "nodemailer": "^6.9.16", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-select": "^5.8.3", "react-toastify": "^10.0.6", "recharts": "^2.15.0", "server-only": "^0.0.1", "simplex-noise": "^4.0.3", "superjson": "^2.2.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.4", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.23.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/eslint": "^8.56.10", "@types/node": "^20.17.6", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "bcrypt": "^5.1.1", "eslint": "^8.57.0", "eslint-config-next": "^14.2.4", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^5.14.0", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "typescript": "^5.6.3"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "npm@10.5.0"}